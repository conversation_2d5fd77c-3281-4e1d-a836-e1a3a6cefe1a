#!/usr/bin/env python3
"""
Initialize the database with required tables and initial data.
"""
import sys
from pathlib import Path

# Add the project root to the Python path
sys.path.insert(0, str(Path(__file__).resolve().parent.parent))

def main():
    """Initialize the database and create initial data."""
    from database.session import engine, Base, init_db
    from sqlalchemy_utils import database_exists, create_database
    import logging
    
    # Set up logging
    logging.basicConfig(level=logging.INFO)
    logger = logging.getLogger(__name__)
    
    # Get database URL from environment or use default
    db_url = str(engine.url)
    
    try:
        # Create database if it doesn't exist (for SQLite, this happens automatically)
        if not database_exists(db_url):
            logger.info(f"Creating database: {db_url}")
            create_database(db_url)
        
        # Create all tables
        logger.info("Creating database tables...")
        init_db()
        
        # Create initial data
        from database.initial_data import create_initial_data
        logger.info("Creating initial data...")
        create_initial_data()
        
        logger.info("Database initialization completed successfully!")
        return True
        
    except Exception as e:
        logger.error(f"Error initializing database: {e}", exc_info=True)
        return False

if __name__ == "__main__":
    if main():
        sys.exit(0)
    else:
        sys.exit(1)
