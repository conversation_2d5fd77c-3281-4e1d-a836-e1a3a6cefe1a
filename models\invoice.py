from sqlalchemy import Column, Integer, String, Numeric, DateTime, ForeignKey, Enum, Boolean, Text
from sqlalchemy.orm import relationship
from datetime import datetime
import enum
from .base import BaseModel

class InvoiceStatus(enum.Enum):
    DRAFT = "draft"
    SENT = "sent"
    PAID = "paid"
    OVERDUE = "overdue"
    CANCELLED = "cancelled"

class PaymentMethod(enum.Enum):
    CASH = "cash"
    CREDIT_CARD = "credit_card"
    BANK_TRANSFER = "bank_transfer"
    CHECK = "check"
    OTHER = "other"

class Invoice(BaseModel):
    """Invoice model for sales transactions."""
    __tablename__ = "invoices"
    
    # Invoice information
    invoice_number = Column(String(20), unique=True, index=True, nullable=False)
    invoice_date = Column(DateTime, default=datetime.utcnow, nullable=False)
    due_date = Column(DateTime, nullable=False)
    
    # Status and type
    status = Column(Enum(InvoiceStatus), default=InvoiceStatus.DRAFT)
    is_paid = Column(Boolean, default=False)
    
    # Totals
    subtotal = Column(Numeric(15, 2), default=0.0)
    tax_amount = Column(Numeric(15, 2), default=0.0)
    discount_amount = Column(Numeric(15, 2), default=0.0)
    total_amount = Column(Numeric(15, 2), default=0.0)
    
    # Payment information
    payment_method = Column(Enum(PaymentMethod), nullable=True)
    payment_date = Column(DateTime, nullable=True)
    payment_reference = Column(String(100), nullable=True)
    
    # Notes
    notes = Column(Text, nullable=True)
    terms_and_conditions = Column(Text, nullable=True)
    
    # Relationships
    customer_id = Column(Integer, ForeignKey('customers.id'), nullable=False)
    customer = relationship("Customer", back_populates="invoices")
    
    user_id = Column(Integer, ForeignKey('users.id'), nullable=False)
    user = relationship("User")
    
    lines = relationship("InvoiceLine", back_populates="invoice", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<Invoice {self.invoice_number}>"
    
    def calculate_totals(self):
        """Calculate invoice totals based on line items."""
        self.subtotal = sum(line.total for line in self.lines)
        self.total_amount = self.subtotal + self.tax_amount - self.discount_amount
    
    def mark_as_paid(self, payment_method, reference=None):
        """Mark invoice as paid."""
        self.status = InvoiceStatus.PAID
        self.is_paid = True
        self.payment_date = datetime.utcnow()
        self.payment_method = payment_method
        self.payment_reference = reference


class InvoiceLine(BaseModel):
    """Invoice line items."""
    __tablename__ = "invoice_lines"
    
    # Relationships
    invoice_id = Column(Integer, ForeignKey('invoices.id'), nullable=False)
    invoice = relationship("Invoice", back_populates="lines")
    
    product_id = Column(Integer, ForeignKey('products.id'), nullable=False)
    product = relationship("Product", back_populates="invoice_lines")
    
    # Line item details
    description = Column(String(200), nullable=False)
    quantity = Column(Numeric(15, 3), default=1.0)  # Allow decimal quantities
    unit_price = Column(Numeric(15, 2), nullable=False)
    tax_rate = Column(Numeric(5, 2), default=0.0)  # Tax rate as percentage
    discount_percent = Column(Numeric(5, 2), default=0.0)  # Discount as percentage
    
    @property
    def subtotal(self):
        """Calculate line subtotal before tax and discount."""
        return self.quantity * self.unit_price
    
    @property
    def discount_amount(self):
        """Calculate discount amount for this line."""
        return self.subtotal * (self.discount_percent / 100)
    
    @property
    def taxable_amount(self):
        """Calculate amount subject to tax."""
        return self.subtotal - self.discount_amount
    
    @property
    def tax_amount(self):
        """Calculate tax amount for this line."""
        return self.taxable_amount * (self.tax_rate / 100)
    
    @property
    def total(self):
        """Calculate total for this line including tax and discount."""
        return self.subtotal + self.tax_amount - self.discount_amount
    
    def __repr__(self):
        return f"<InvoiceLine {self.product.code} x {self.quantity}>"
