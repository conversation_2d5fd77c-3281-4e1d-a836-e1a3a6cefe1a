"""
Resource management for the application.

This module provides utilities for loading and managing application resources
such as icons, images, stylesheets, and other static assets.
"""
import os
import sys
from pathlib import Path
from typing import Optional, Union, Dict, Any
from PyQt6.QtGui import QIcon, QPixmap, QFont, QColor
from PyQt6.QtCore import Qt, QSize
from PyQt6.QtWidgets import QApplication

from config.settings import PATHS

class ResourceManager:
    """Manages application resources like icons, images, and styles."""
    
    _instance = None
    _resources_loaded = False
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(ResourceManager, cls).__new__(cls)
            cls._instance._init_resources()
        return cls._instance
    
    def _init_resources(self):
        """Initialize the resource manager."""
        if self._resources_loaded:
            return
            
        self._icons = {}
        self._images = {}
        self._styles = {}
        self._fonts = {}
        
        # Load default resources
        self._load_default_resources()
        
        self._resources_loaded = True
    
    def _load_default_resources(self):
        """Load default application resources."""
        # Create resources directory if it doesn't exist
        resources_dir = PATHS.data / 'resources'
        resources_dir.mkdir(exist_ok=True, parents=True)
        
        # Default icon paths (will be created if they don't exist)
        icon_paths = {
            'app': 'icons/app_icon.png',
            'new': 'icons/new_file.png',
            'open': 'icons/open_file.png',
            'save': 'icons/save.png',
            'save_as': 'icons/save_as.png',
            'print': 'icons/print.png',
            'cut': 'icons/cut.png',
            'copy': 'icons/copy.png',
            'paste': 'icons/paste.png',
            'delete': 'icons/delete.png',
            'refresh': 'icons/refresh.png',
            'settings': 'icons/settings.png',
            'help': 'icons/help.png',
            'about': 'icons/about.png',
            'exit': 'icons/exit.png',
        }
        
        # Create default icons if they don't exist
        for name, rel_path in icon_paths.items():
            path = resources_dir / rel_path
            path.parent.mkdir(exist_ok=True, parents=True)
            
            # If the icon doesn't exist, create a placeholder
            if not path.exists():
                self._create_placeholder_icon(path, name)
            
            # Load the icon
            self._icons[name] = QIcon(str(path))
    
    def _create_placeholder_icon(self, path: Path, name: str):
        """Create a placeholder icon if it doesn't exist."""
        from PyQt6.QtGui import QPainter, QPen, QBrush, QColorConstants
        from PyQt6.QtGui import QImage, QPixmap
        
        # Create a simple colored rectangle with text
        size = 32
        image = QImage(size, size, QImage.Format.Format_ARGB32)
        image.fill(QColorConstants.Transparent)
        
        painter = QPainter(image)
        try:
            # Draw a colored rectangle
            color = QColor(70, 130, 180)  # Steel blue
            painter.setBrush(QBrush(color))
            painter.setPen(QPen(QColorConstants.Black, 1))
            painter.drawRoundedRect(2, 2, size-4, size-4, 4, 4)
            
            # Add text
            painter.setPen(QPen(QColorConstants.White, 1))
            font = painter.font()
            font.setBold(True)
            font.setPixelSize(14)
            painter.setFont(font)
            
            # Use the first letter of the name as the icon
            text = name[0].upper() if name else '?'
            painter.drawText(image.rect(), Qt.AlignmentFlag.AlignCenter, text)
            
        finally:
            painter.end()
        
        # Save the image
        image.save(str(path))
    
    def get_icon(self, name: str) -> QIcon:
        """Get an icon by name."""
        return self._icons.get(name, QIcon())
    
    def get_pixmap(self, name: str, size: QSize = None) -> QPixmap:
        """Get a pixmap by name and optional size."""
        icon = self.get_icon(name)
        if not icon.isNull() and size:
            return icon.pixmap(size)
        return icon.pixmap(QSize(32, 32))  # Default size
    
    def get_style(self, name: str) -> str:
        """Get a style sheet by name."""
        return self._styles.get(name, '')
    
    def get_font(self, name: str) -> QFont:
        """Get a font by name."""
        return self._fonts.get(name, QApplication.font())
    
    def load_stylesheet(self, name: str, path: Union[str, Path]) -> bool:
        """Load a stylesheet from a file."""
        try:
            with open(path, 'r', encoding='utf-8') as f:
                self._styles[name] = f.read()
            return True
        except Exception as e:
            print(f"Error loading stylesheet {name}: {e}", file=sys.stderr)
            return False
    
    def load_font(self, name: str, path: Union[str, Path], size: int = -1) -> bool:
        """Load a font from a file."""
        try:
            font_id = QFontDatabase.addApplicationFont(str(path))
            if font_id == -1:
                return False
                
            font_family = QFontDatabase.applicationFontFamilies(font_id)[0]
            font = QFont(font_family, size if size > 0 else QApplication.font().pointSize())
            self._fonts[name] = font
            return True
        except Exception as e:
            print(f"Error loading font {name}: {e}", file=sys.stderr)
            return False

# Create a global instance of the resource manager
resources = ResourceManager()

def get_icon(name: str) -> QIcon:
    """Get an icon by name from the global resource manager."""
    return resources.get_icon(name)

def get_pixmap(name: str, size: QSize = None) -> QPixmap:
    """Get a pixmap by name from the global resource manager."""
    return resources.get_pixmap(name, size)

def get_style(name: str) -> str:
    """Get a style sheet by name from the global resource manager."""
    return resources.get_style(name)

def get_font(name: str) -> QFont:
    """Get a font by name from the global resource manager."""
    return resources.get_font(name)

def load_stylesheet(name: str, path: Union[str, Path]) -> bool:
    """Load a stylesheet from a file using the global resource manager."""
    return resources.load_stylesheet(name, path)

def load_font(name: str, path: Union[str, Path], size: int = -1) -> bool:
    """Load a font from a file using the global resource manager."""
    return resources.load_font(name, path, size)

# Initialize the resource manager when the module is imported
resources._init_resources()
