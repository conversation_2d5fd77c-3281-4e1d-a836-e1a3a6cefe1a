"""
Database session management for SQLAlchemy.
"""
from sqlalchemy import create_engine, URL
from sqlalchemy.orm import sessionmaker, scoped_session
import os
from pathlib import Path
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Import settings and base model
from config.settings import DB_CONFIG
from models.base import Base

# Create database URL based on configuration
if DB_CONFIG['drivername'] == 'sqlite':
    # SQLite connection
    DATABASE_URL = f"sqlite:///{DB_CONFIG['database']}"
    connect_args = {"check_same_thread": False}
else:
    # SQL Server connection
    DATABASE_URL = URL.create(
        drivername=DB_CONFIG['drivername'],
        username=DB_CONFIG.get('username'),
        password=DB_CONFIG.get('password'),
        host=DB_CONFIG.get('host'),
        port=DB_CONFIG.get('port'),
        database=DB_CONFIG.get('database'),
        query=DB_CONFIG.get('query', {})
    )
    connect_args = {}

# Create SQLAlchemy engine
engine = create_engine(
    DATABASE_URL,
    connect_args=connect_args,
    echo=True  # Enable SQL query logging for debugging
)

# Create a scoped session factory
SessionLocal = scoped_session(
    sessionmaker(autocommit=False, autoflush=False, bind=engine)
)

def get_db():
    """
    Dependency function to get DB session.
    Use this in your FastAPI dependencies or other contexts.
    """
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

def init_db():
    """
    Initialize the database by creating all tables.
    Call this function when the application starts.
    """
    # Import all models here to ensure they are registered with SQLAlchemy
    from models.user import User
    from models.customer import Customer
    from models.product import Product, ProductCategory
    from models.invoice import Invoice, InvoiceLine
    
    # Create all tables
    Base.metadata.create_all(bind=engine)
    
    return True
