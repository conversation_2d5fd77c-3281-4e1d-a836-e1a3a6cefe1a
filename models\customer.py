from sqlalchemy import Column, String, Integer, Text, Numeric, ForeignKey, DateTime, Boolean
from sqlalchemy.orm import relationship
from datetime import datetime
from .base import BaseModel

class Customer(BaseModel):
    """Customer model for storing customer information."""
    __tablename__ = "customers"
    
    # Basic information
    name = Column(String(100), nullable=False, index=True)
    code = Column(String(20), unique=True, index=True, nullable=False)
    contact_person = Column(String(100), nullable=True)
    
    # Contact information
    phone = Column(String(20), nullable=True)
    mobile = Column(String(20), nullable=True)
    email = Column(String(100), nullable=True)
    
    # Address information
    address = Column(Text, nullable=True)
    city = Column(String(50), nullable=True)
    state = Column(String(50), nullable=True)
    postal_code = Column(String(20), nullable=True)
    country = Column(String(50), nullable=True)
    
    # Financial information
    tax_id = Column(String(50), nullable=True)
    credit_limit = Column(Numeric(15, 2), default=0.0)
    current_balance = Column(Numeric(15, 2), default=0.0)
    
    # Additional information
    notes = Column(Text, nullable=True)
    is_active = Column(Boolean, default=True)
    
    # Relationships
    invoices = relationship("Invoice", back_populates="customer")
    
    def __repr__(self):
        return f"<Customer {self.code} - {self.name}>"
    
    @property
    def full_address(self):
        """Return formatted full address."""
        parts = [self.address, self.city, self.state, self.postal_code, self.country]
        return ", ".join(filter(None, parts))
