"""
Application settings and configuration.

This module contains all the configuration settings for the application,
including database connections, UI settings, and other application-specific
configuration options.
"""
import os
import logging
from pathlib import Path
from dotenv import load_dotenv
from enum import Enum

# Load environment variables from .env file
load_dotenv()

# Base directory
BASE_DIR = Path(__file__).resolve().parent.parent

# Logging configuration
LOG_LEVEL = os.getenv('LOG_LEVEL', 'INFO').upper()
LOG_DIR = BASE_DIR / 'logs'
LOG_DIR.mkdir(exist_ok=True)

logging.basicConfig(
    level=LOG_LEVEL,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(LOG_DIR / 'app.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

# Database settings
def get_database_config():
    """Get database configuration based on environment variables."""
    driver = os.getenv('DB_DRIVER', 'sqlite')
    
    if driver == 'sqlite':
        # SQLite configuration
        db_path = os.getenv('SQLITE_PATH', str(BASE_DIR / 'data' / 'rabie_alkhaleej.db'))
        # Ensure the directory exists
        os.makedirs(os.path.dirname(db_path), exist_ok=True)
        return {
            'drivername': 'sqlite',
            'database': db_path
        }
    else:
        # SQL Server configuration
        return {
            'drivername': driver,
            'username': os.getenv('DB_USER', ''),
            'password': os.getenv('DB_PASSWORD', ''),
            'host': os.getenv('DB_HOST', 'localhost'),
            'port': os.getenv('DB_PORT', ''),
            'database': os.getenv('DB_NAME', 'RabieAlKhaleej'),
            'query': {'driver': os.getenv('DB_DRIVER_NAME', '')} if driver == 'mssql+pyodbc' else {}
        }

DB_CONFIG = get_database_config()

# Application settings
class AppSettings:
    """Application settings and metadata."""
    NAME = "Rabie Al-Khaleej Accounting"
    VERSION = "1.0.0"
    DESCRIPTION = "A comprehensive accounting and business management solution"
    COMPANY = "Rabie Al-Khaleej"
    COPYRIGHT = f"© 2025 {COMPANY}. All rights reserved."
    
    # Application modes
    class Mode(Enum):
        DEVELOPMENT = 'development'
        TESTING = 'testing'
        PRODUCTION = 'production'
    
    # Current application mode
    ENV = Mode(os.getenv('APP_ENV', 'development').lower())
    DEBUG = ENV != Mode.PRODUCTION
    TESTING = ENV == Mode.TESTING

# UI Settings
class UISettings:
    """User Interface settings."""
    # Window settings
    WINDOW = {
        'default_width': 1280,
        'default_height': 800,
        'min_width': 1024,
        'min_height': 768,
        'theme': 'Fusion'  # Options: 'Fusion', 'Windows', 'WindowsVista', etc.
    }
    
    # Table settings
    TABLE = {
        'row_height': 25,
        'header_height': 30,
        'alternate_row_colors': True,
        'grid_style': 'solid',
        'grid_color': '#e0e0e0',
        'selection_behavior': 'selectRows',
        'selection_mode': 'extendedSelection',
        'sorting_enabled': True
    }
    
    # Font settings
    FONTS = {
        'default_family': 'Segoe UI',
        'default_size': 10,
        'header_size': 12,
        'title_size': 14,
        'small_size': 8
    }

# Path configurations
class Paths:
    """Application paths and directories."""
    def __init__(self):
        # Base directories
        self.base = BASE_DIR
        self.app = self.base / 'app'
        self.database = self.base / 'database'
        self.ui = self.base / 'ui'
        self.utils = self.base / 'utils'
        
        # Data directories
        self.data = self.base / 'data'
        self.reports = self.data / 'reports'
        self.templates = self.data / 'templates'
        self.temp = self.data / 'temp'
        self.backups = self.data / 'backups'
        
        # Create directories if they don't exist
        for path in [self.data, self.reports, self.templates, 
                    self.temp, self.backups]:
            path.mkdir(parents=True, exist_ok=True)
    
    def __getitem__(self, key):
        return getattr(self, key, None)

# Initialize paths
PATHS = Paths()

# Export commonly used settings
APP_NAME = AppSettings.NAME
VERSION = AppSettings.VERSION
UI_SETTINGS = UISettings().WINDOW

# Create a dictionary of all settings for easy serialization
SETTINGS = {
    'app': {k: v for k, v in vars(AppSettings).items() if not k.startswith('_')},
    'ui': {k: v for k, v in vars(UISettings).items() if not k.startswith('_')},
    'paths': {k: v for k, v in vars(PATHS).items() if not k.startswith('_') and not callable(v)}
}

# Log configuration
logger.info(f"Application starting in {AppSettings.ENV.value} mode")
if DB_CONFIG['drivername'] == 'sqlite':
    logger.info(f"Database: sqlite:///{DB_CONFIG['database']}")
else:
    logger.info(f"Database: {DB_CONFIG['drivername']}://{'*' * 8}@{DB_CONFIG.get('host', 'localhost')}/{DB_CONFIG['database']}")
