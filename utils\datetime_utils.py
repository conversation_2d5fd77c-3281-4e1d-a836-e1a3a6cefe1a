"""
Date and time utility functions.

This module provides utility functions for working with dates and times,
including parsing, formatting, and timezone handling.
"""
from datetime import datetime, date, time, timedelta, timezone
from typing import Union, Optional, Tuple, List, Dict, Any
import re
import pytz
from dateutil import parser, tz

from .exceptions import AppError

# Common date/time formats
DATE_FORMAT = "%Y-%m-%d"
TIME_FORMAT = "%H:%M:%S"
DATETIME_FORMAT = f"{DATE_FORMAT} {TIME_FORMAT}"
ISO_DATETIME_FORMAT = "%Y-%m-%dT%H:%M:%S%z"

# Timezone constants
UTC = timezone.utc
LOCAL_TZ = tz.gettz()  # System local timezone

def now(tzinfo: timezone = None) -> datetime:
    """
    Get the current datetime, optionally in the specified timezone.
    
    Args:
        tzinfo: Timezone to localize the datetime to (default: UTC)
        
    Returns:
        Current datetime in the specified timezone
    """
    dt = datetime.now(UTC)
    if tzinfo is not None:
        dt = dt.astimezone(tzinfo)
    return dt


def today(tzinfo: timezone = None) -> date:
    """
    Get the current date, optionally in the specified timezone.
    
    Args:
        tzinfo: Timezone to get the date for (default: UTC)
        
    Returns:
        Current date in the specified timezone
    """
    return now(tzinfo).date()


def parse_datetime(
    dt_str: str, 
    tzinfo: timezone = None,
    default_tz: timezone = None
) -> datetime:
    """
    Parse a datetime string into a timezone-aware datetime object.
    
    Args:
        dt_str: Datetime string to parse
        tzinfo: Timezone to convert the datetime to (default: keep original or use default_tz)
        default_tz: Timezone to use if the input string has no timezone (default: UTC)
        
    Returns:
        Timezone-aware datetime object
        
    Raises:
        AppError: If the datetime string cannot be parsed
    """
    if not dt_str:
        raise AppError("Empty datetime string")
    
    try:
        # Try parsing with dateutil.parser
        dt = parser.parse(dt_str, default=datetime(2000, 1, 1))
        
        # If no timezone info, use the default timezone
        if dt.tzinfo is None and default_tz is not None:
            dt = dt.replace(tzinfo=default_tz)
        
        # Convert to the target timezone if specified
        if tzinfo is not None and dt.tzinfo is not None:
            dt = dt.astimezone(tzinfo)
        
        return dt
    except (ValueError, OverflowError) as e:
        raise AppError(f"Invalid datetime string: {dt_str}") from e


def format_datetime(
    dt: Union[datetime, date, str],
    format_str: str = DATETIME_FORMAT,
    tzinfo: timezone = None
) -> str:
    """
    Format a datetime object as a string.
    
    Args:
        dt: Datetime, date, or datetime string to format
        format_str: Format string (default: DATETIME_FORMAT)
        tzinfo: Timezone to convert the datetime to before formatting
        
    Returns:
        Formatted datetime string
        
    Raises:
        AppError: If the input cannot be formatted
    """
    if not dt:
        return ""
    
    # Convert string to datetime if needed
    if isinstance(dt, str):
        try:
            dt = parse_datetime(dt)
        except AppError:
            return dt  # Return original string if it can't be parsed
    
    # Convert timezone if needed
    if isinstance(dt, datetime) and tzinfo is not None and dt.tzinfo is not None:
        dt = dt.astimezone(tzinfo)
    
    try:
        return dt.strftime(format_str)
    except (ValueError, AttributeError) as e:
        raise AppError(f"Failed to format datetime: {e}") from e


def time_ago(
    dt: Union[datetime, str],
    now_dt: datetime = None,
    short: bool = False
) -> str:
    """
    Format a datetime as a human-readable string indicating how long ago it was.
    
    Args:
        dt: The datetime to format
        now_dt: Reference datetime (default: current time)
        short: Whether to use short format (e.g., "2h ago" instead of "2 hours ago")
        
    Returns:
        Human-readable time difference string
    """
    if not dt:
        return ""
    
    # Parse datetime string if needed
    if isinstance(dt, str):
        try:
            dt = parse_datetime(dt)
        except AppError:
            return dt
    
    # Get current time if not provided
    now_dt = now_dt or now(dt.tzinfo if isinstance(dt, datetime) and dt.tzinfo else None)
    
    # Handle date objects
    if isinstance(dt, date) and not isinstance(dt, datetime):
        dt = datetime.combine(dt, time())
    
    # Calculate time difference
    delta = now_dt - dt if now_dt > dt else dt - now_dt
    
    # Format the time difference
    seconds = int(delta.total_seconds())
    
    if seconds < 60:
        value = seconds
        unit = "s" if short else " second" + ("" if value == 1 else "s")
    elif seconds < 3600:
        value = seconds // 60
        unit = "m" if short else " minute" + ("" if value == 1 else "s")
    elif seconds < 86400:
        value = seconds // 3600
        unit = "h" if short else " hour" + ("" if value == 1 else "s")
    elif seconds < 2592000:  # ~30 days
        value = seconds // 86400
        unit = "d" if short else " day" + ("" if value == 1 else "s")
    elif seconds < 31536000:  # ~365 days
        value = seconds // 2592000
        unit = "mo" if short else " month" + ("" if value == 1 else "s")
    else:
        value = seconds // 31536000
        unit = "y" if short else " year" + ("" if value == 1 else "s")
    
    if short:
        return f"{value}{unit} ago" if now_dt > dt else f"in {value}{unit}"
    else:
        return f"{value}{unit} ago" if now_dt > dt else f"in {value}{unit}"


def parse_duration(duration_str: str) -> timedelta:
    """
    Parse a duration string into a timedelta.
    
    Supported formats:
    - "5m" or "5 min" or "5 minutes"
    - "2h" or "2 hours"
    - "3d" or "3 days"
    - "1w" or "1 week"
    - Combinations like "1h30m" or "2d 5h 10m"
    
    Args:
        duration_str: Duration string to parse
        
    Returns:
        timedelta representing the duration
        
    Raises:
        AppError: If the duration string is invalid
    """
    if not duration_str:
        raise AppError("Empty duration string")
    
    # Normalize the string
    duration_str = duration_str.strip().lower()
    
    # Check for common patterns
    if duration_str == "now":
        return timedelta()
    
    # Parse the duration string
    pattern = r"""
        (\d+)      # One or more digits
        \s*        # Optional whitespace
        ([smhdwy])  # Time unit (s, m, h, d, w, y)
        |
        (\d+)\s*(?:min|mins|minute|minutes)
        |
        (\d+)\s*(?:hour|hours|hr|hrs)
        |
        (\d+)\s*(?:day|days)
        |
        (\d+)\s*(?:week|weeks|wk|wks)
        |
        (\d+)\s*(?:month|months|mo|mos)
        |
        (\d+)\s*(?:year|years|yr|yrs)
    """
    
    matches = re.findall(pattern, duration_str, re.VERBOSE)
    if not matches:
        raise AppError(f"Invalid duration format: {duration_str}")
    
    total_seconds = 0
    
    for match in matches:
        # Handle short format (e.g., "5m", "2h")
        if match[0] and match[1]:
            value = int(match[0])
            unit = match[1]
            
            if unit == 's':
                total_seconds += value
            elif unit == 'm':
                total_seconds += value * 60
            elif unit == 'h':
                total_seconds += value * 3600
            elif unit == 'd':
                total_seconds += value * 86400
            elif unit == 'w':
                total_seconds += value * 604800
            elif unit == 'y':
                total_seconds += value * 31536000  # Approximate
        
        # Handle minutes
        elif match[2]:
            total_seconds += int(match[2]) * 60
        
        # Handle hours
        elif match[3]:
            total_seconds += int(match[3]) * 3600
        
        # Handle days
        elif match[4]:
            total_seconds += int(match[4]) * 86400
        
        # Handle weeks
        elif match[5]:
            total_seconds += int(match[5]) * 604800
        
        # Handle months (approximate)
        elif match[6]:
            total_seconds += int(match[6]) * 2592000  # 30 days
        
        # Handle years (approximate)
        elif match[7]:
            total_seconds += int(match[7]) * 31536000  # 365 days
    
    return timedelta(seconds=total_seconds)


def format_duration(
    td: timedelta,
    precision: str = 'auto',
    max_units: int = 2,
    short: bool = False
) -> str:
    """
    Format a timedelta as a human-readable string.
    
    Args:
        td: The timedelta to format
        precision: The smallest unit to include ('auto', 'seconds', 'minutes', 'hours', 'days')
        max_units: Maximum number of time units to include
        short: Whether to use short format (e.g., "2h" instead of "2 hours")
        
    Returns:
        Formatted duration string
    """
    if not isinstance(td, timedelta):
        return str(td)
    
    # Convert to seconds
    total_seconds = int(td.total_seconds())
    is_negative = total_seconds < 0
    total_seconds = abs(total_seconds)
    
    if total_seconds == 0:
        return "0s" if short else "0 seconds"
    
    # Define time units in seconds
    units = [
        ('year', 31536000),    # 365 days
        ('month', 2592000),    # 30 days
        ('week', 604800),      # 7 days
        ('day', 86400),        # 24 hours
        ('hour', 3600),        # 60 minutes
        ('minute', 60),
        ('second', 1)
    ]
    
    # Find the appropriate precision
    if precision == 'auto':
        # Default to seconds for durations < 1 minute
        if total_seconds < 60:
            precision = 'seconds'
        else:
            precision = 'minutes'  # Default precision for longer durations
    
    # Find the index of the precision unit
    precision_index = next(
        (i for i, (unit, _) in enumerate(units) if unit.startswith(precision.rstrip('s'))),
        len(units) - 1  # Default to seconds if precision not found
    )
    
    # Calculate the time components
    components = []
    remaining = total_seconds
    
    for i, (unit_name, unit_seconds) in enumerate(units):
        if i > precision_index:
            continue
            
        value = remaining // unit_seconds
        if value > 0 or (i == precision_index and not components):
            # Add the component if it's non-zero or if it's the precision unit
            # and we haven't added any components yet
            if short:
                # Use short unit names
                short_units = {
                    'year': 'y',
                    'month': 'mo',
                    'week': 'w',
                    'day': 'd',
                    'hour': 'h',
                    'minute': 'm',
                    'second': 's'
                }
                unit_display = short_units[unit_name]
            else:
                # Use full unit names with proper pluralization
                unit_display = f" {unit_name}"
                if value != 1:
                    unit_display += 's'
            
            components.append((value, unit_display))
            remaining %= unit_seconds
            
            # Stop if we've reached the maximum number of units
            if len(components) >= max_units:
                break
    
    # Format the components
    if short:
        result = ''.join(f"{value}{unit}" for value, unit in components)
    else:
        if len(components) == 1:
            value, unit = components[0]
            result = f"{value}{unit}"
        else:
            parts = [f"{value}{unit}" for value, unit in components]
            if len(parts) > 1:
                result = ", ".join(parts[:-1]) + f" and {parts[-1]}"
            else:
                result = parts[0]
    
    # Add negative sign if needed
    if is_negative:
        result = f"-{result}"
    
    return result
