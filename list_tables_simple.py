import pyodbc

# Database connection details
db_path = r"c:\HamzaZareiProgram\RabieAl-KhaleejMulti.mdb"
username = "majid"
password = "majid"

# Try different connection strings
conn_strs = [
    f'DRIVER={{Microsoft Access Driver (*.mdb, *.accdb)}};DBQ={db_path}',
    f'DRIVER={{Microsoft Access Driver (*.mdb, *.accdb)}};DBQ={db_path};UID={username};PWD={password};',
    f'DRIVER={{Microsoft Access Driver (*.mdb)}};DBQ={db_path}',
    f'DRIVER={{Microsoft Access Driver (*.mdb)}};DBQ={db_path};UID={username};PWD={password};',
]

# Try to connect
conn = None
for conn_str in conn_strs:
    try:
        print(f"Trying: {conn_str[:80]}...")
        conn = pyodbc.connect(conn_str)
        print("Successfully connected to the database!")
        break
    except pyodbc.Error as e:
        print(f"Failed: {e}")
        continue

if not conn:
    print("Could not connect to the database with any connection string.")
    exit(1)

try:
    # Get all tables
    print("\n=== Tables in the database ===\n")
    cursor = conn.cursor()
    
    # Get tables
    tables = []
    for table in cursor.tables(tableType='TABLE'):
        table_name = table.table_name
        if not table_name.startswith('MSys'):
            tables.append(table_name)
    
    # Print tables and their columns
    for i, table_name in enumerate(sorted(tables), 1):
        print(f"{i}. {table_name}")
        
        # Get columns
        try:
            columns = []
            for col in cursor.columns(table=table_name):
                columns.append(f"  - {col.column_name} ({col.type_name})")
            
            # Print first 5 columns to keep output manageable
            print("\n".join(columns[:5]))
            if len(columns) > 5:
                print(f"  ... and {len(columns) - 5} more columns")
            print()
            
        except Exception as e:
            print(f"  Could not get columns: {e}\n")
    
    print("\n=== Sample Data ===\n")
    
    # Show sample data from the first table
    if tables:
        first_table = tables[0]
        print(f"Sample data from table: {first_table}")
        
        try:
            cursor.execute(f'SELECT TOP 3 * FROM [{first_table}]')
            
            # Get column names
            columns = [column[0] for column in cursor.description]
            print("\nColumns:", ", ".join(columns))
            
            # Get and print sample rows
            print("\nSample Rows:")
            for row in cursor.fetchall():
                print("  ", " | ".join(str(cell) for cell in row))
                
        except Exception as e:
            print(f"  Could not fetch sample data: {e}")
    
except Exception as e:
    print(f"An error occurred: {e}")
    
finally:
    conn.close()
    print("\nDatabase connection closed.")
