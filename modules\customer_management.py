"""
Customer Management Module

This module provides comprehensive customer management functionality including:
- Customer list view with search and filtering
- Add/Edit customer forms
- Customer details view
- Customer deletion with confirmation
"""

from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QPushButton, QTableWidget, QTableWidgetItem,
    QLineEdit, QLabel, QFormLayout, QTextEdit, QMessageBox,
    QDialog, QDialogButtonBox, QHeaderView, QAbstractItemView, QGroupBox,
    QDoubleSpinBox, QFrame, QSplitter, QScrollArea
)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QFont
from sqlalchemy.orm import sessionmaker
from database.session import engine
from models.customer import Customer
import logging

logger = logging.getLogger(__name__)

# Create session factory
Session = sessionmaker(bind=engine)

class CustomerForm(QDialog):
    """Dialog for adding/editing customers."""
    
    def __init__(self, customer=None, parent=None):
        super().__init__(parent)
        self.customer = customer
        self.is_edit_mode = customer is not None
        self.setup_ui()
        if self.is_edit_mode:
            self.load_customer_data()
    
    def setup_ui(self):
        """Set up the customer form UI."""
        self.setWindowTitle("Edit Customer" if self.is_edit_mode else "Add New Customer")
        self.setModal(True)
        self.resize(600, 500)
        
        layout = QVBoxLayout(self)
        
        # Create scroll area for the form
        scroll = QScrollArea()
        scroll_widget = QWidget()
        scroll_layout = QVBoxLayout(scroll_widget)
        
        # Basic Information Group
        basic_group = QGroupBox("Basic Information")
        basic_layout = QFormLayout(basic_group)

        self.name_edit = QLineEdit()
        self.name_edit.setMaxLength(100)
        self.contact_person_edit = QLineEdit()
        self.contact_person_edit.setMaxLength(100)

        basic_layout.addRow("Customer Name*:", self.name_edit)
        basic_layout.addRow("Contact Person:", self.contact_person_edit)
        
        # Contact Information Group
        contact_group = QGroupBox("Contact Information")
        contact_layout = QFormLayout(contact_group)
        
        self.phone_edit = QLineEdit()
        self.phone_edit.setMaxLength(20)
        self.mobile_edit = QLineEdit()
        self.mobile_edit.setMaxLength(20)
        self.email_edit = QLineEdit()
        self.email_edit.setMaxLength(100)
        
        contact_layout.addRow("Phone:", self.phone_edit)
        contact_layout.addRow("Mobile:", self.mobile_edit)
        contact_layout.addRow("Email:", self.email_edit)
        
        # Address Information Group
        address_group = QGroupBox("Address Information")
        address_layout = QFormLayout(address_group)
        
        self.address_edit = QTextEdit()
        self.address_edit.setMaximumHeight(80)
        self.city_edit = QLineEdit()
        self.city_edit.setMaxLength(50)
        self.state_edit = QLineEdit()
        self.state_edit.setMaxLength(50)
        self.postal_code_edit = QLineEdit()
        self.postal_code_edit.setMaxLength(20)
        self.country_edit = QLineEdit()
        self.country_edit.setMaxLength(50)
        
        address_layout.addRow("Address:", self.address_edit)
        address_layout.addRow("City:", self.city_edit)
        address_layout.addRow("State:", self.state_edit)
        address_layout.addRow("Postal Code:", self.postal_code_edit)
        address_layout.addRow("Country:", self.country_edit)
        
        # Financial Information Group
        financial_group = QGroupBox("Financial Information")
        financial_layout = QFormLayout(financial_group)
        
        self.tax_id_edit = QLineEdit()
        self.tax_id_edit.setMaxLength(50)
        self.credit_limit_spin = QDoubleSpinBox()
        self.credit_limit_spin.setMaximum(*********.99)
        self.credit_limit_spin.setDecimals(2)
        self.current_balance_spin = QDoubleSpinBox()
        self.current_balance_spin.setMaximum(*********.99)
        self.current_balance_spin.setDecimals(2)
        self.current_balance_spin.setReadOnly(True)  # Balance is calculated
        
        financial_layout.addRow("Tax ID:", self.tax_id_edit)
        financial_layout.addRow("Credit Limit:", self.credit_limit_spin)
        financial_layout.addRow("Current Balance:", self.current_balance_spin)
        
        # Additional Information Group
        additional_group = QGroupBox("Additional Information")
        additional_layout = QFormLayout(additional_group)

        self.notes_edit = QTextEdit()
        self.notes_edit.setMaximumHeight(80)

        additional_layout.addRow("Notes:", self.notes_edit)
        
        # Add all groups to scroll layout
        scroll_layout.addWidget(basic_group)
        scroll_layout.addWidget(contact_group)
        scroll_layout.addWidget(address_group)
        scroll_layout.addWidget(financial_group)
        scroll_layout.addWidget(additional_group)
        
        scroll.setWidget(scroll_widget)
        scroll.setWidgetResizable(True)
        layout.addWidget(scroll)
        
        # Button box
        button_box = QDialogButtonBox(
            QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel
        )
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)
        
        # Set required field indicators
        self.code_edit.setStyleSheet("QLineEdit { border: 2px solid #ff6b6b; }")
        self.name_edit.setStyleSheet("QLineEdit { border: 2px solid #ff6b6b; }")
        
        # Connect validation
        self.code_edit.textChanged.connect(self.validate_form)
        self.name_edit.textChanged.connect(self.validate_form)
        
        self.validate_form()
    
    def validate_form(self):
        """Validate form fields and enable/disable OK button."""
        is_valid = bool(self.code_edit.text().strip() and self.name_edit.text().strip())
        
        # Update field styling
        code_style = "QLineEdit { border: 2px solid #4CAF50; }" if self.code_edit.text().strip() else "QLineEdit { border: 2px solid #ff6b6b; }"
        name_style = "QLineEdit { border: 2px solid #4CAF50; }" if self.name_edit.text().strip() else "QLineEdit { border: 2px solid #ff6b6b; }"
        
        self.code_edit.setStyleSheet(code_style)
        self.name_edit.setStyleSheet(name_style)
        
        # Enable/disable OK button
        button_box = self.findChild(QDialogButtonBox)
        if button_box:
            ok_button = button_box.button(QDialogButtonBox.StandardButton.Ok)
            ok_button.setEnabled(is_valid)
    
    def load_customer_data(self):
        """Load customer data into form fields."""
        if not self.customer:
            return
        
        self.name_edit.setText(self.customer.name or "")
        self.contact_person_edit.setText(self.customer.contact_person or "")
        self.phone_edit.setText(self.customer.phone or "")
        self.mobile_edit.setText(self.customer.mobile or "")
        self.email_edit.setText(self.customer.email or "")
        self.address_edit.setPlainText(self.customer.address or "")
        self.city_edit.setText(self.customer.city or "")
        self.state_edit.setText(self.customer.state or "")
        self.postal_code_edit.setText(self.customer.postal_code or "")
        self.country_edit.setText(self.customer.country or "")
        self.tax_id_edit.setText(self.customer.tax_id or "")
        self.credit_limit_spin.setValue(float(self.customer.credit_limit or 0))
        self.current_balance_spin.setValue(float(self.customer.current_balance or 0))
        self.notes_edit.setPlainText(self.customer.notes or "")
    
    def get_customer_data(self):
        """Get customer data from form fields."""
        return {
            'name': self.name_edit.text().strip(),
            'contact_person': self.contact_person_edit.text().strip() or None,
            'phone': self.phone_edit.text().strip() or None,
            'mobile': self.mobile_edit.text().strip() or None,
            'email': self.email_edit.text().strip() or None,
            'address': self.address_edit.toPlainText().strip() or None,
            'city': self.city_edit.text().strip() or None,
            'state': self.state_edit.text().strip() or None,
            'postal_code': self.postal_code_edit.text().strip() or None,
            'country': self.country_edit.text().strip() or None,
            'tax_id': self.tax_id_edit.text().strip() or None,
            'credit_limit': self.credit_limit_spin.value(),
            'current_balance': self.current_balance_spin.value(),
            'notes': self.notes_edit.toPlainText().strip() or None
        }


class CustomerListWidget(QWidget):
    """Widget for displaying and managing customer list."""
    
    customer_selected = pyqtSignal(object)  # Emits selected customer
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
        self.load_customers()
    
    def setup_ui(self):
        """Set up the customer list UI."""
        layout = QVBoxLayout(self)
        
        # Search and filter section
        search_layout = QHBoxLayout()
        
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("Search customers by name, email, or phone...")
        self.search_edit.textChanged.connect(self.filter_customers)

        search_layout.addWidget(QLabel("Search:"))
        search_layout.addWidget(self.search_edit, 1)
        
        layout.addLayout(search_layout)
        
        # Customer table
        self.table = QTableWidget()
        self.table.setColumnCount(6)
        self.table.setHorizontalHeaderLabels([
            "Name", "Contact Person", "Phone", "Email", "City", "Balance"
        ])
        
        # Configure table
        self.table.setSelectionBehavior(QAbstractItemView.SelectionBehavior.SelectRows)
        self.table.setSelectionMode(QAbstractItemView.SelectionMode.SingleSelection)
        self.table.setAlternatingRowColors(True)
        self.table.setSortingEnabled(True)
        
        # Set column widths
        header = self.table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.Fixed)  # Code
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.Stretch)  # Name
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.ResizeToContents)  # Contact
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.ResizeToContents)  # Phone
        header.setSectionResizeMode(4, QHeaderView.ResizeMode.ResizeToContents)  # Email
        header.setSectionResizeMode(5, QHeaderView.ResizeMode.ResizeToContents)  # City
        header.setSectionResizeMode(6, QHeaderView.ResizeMode.ResizeToContents)  # Balance
        header.setSectionResizeMode(7, QHeaderView.ResizeMode.Fixed)  # Status
        
        self.table.setColumnWidth(0, 80)  # Code
        self.table.setColumnWidth(7, 80)  # Status
        
        # Connect selection signal
        self.table.itemSelectionChanged.connect(self.on_selection_changed)
        
        layout.addWidget(self.table)
        
        # Store all customers for filtering
        self.all_customers = []
    
    def load_customers(self):
        """Load customers from database."""
        try:
            session = Session()
            self.all_customers = session.query(Customer).order_by(Customer.name).all()
            session.close()
            self.populate_table(self.all_customers)
        except Exception as e:
            logger.error(f"Error loading customers: {e}")
            QMessageBox.critical(self, "Error", f"Failed to load customers: {str(e)}")
    
    def populate_table(self, customers):
        """Populate table with customer data."""
        self.table.setRowCount(len(customers))

        for row, customer in enumerate(customers):
            self.table.setItem(row, 0, QTableWidgetItem(customer.name or ""))
            self.table.setItem(row, 1, QTableWidgetItem(customer.contact_person or ""))
            self.table.setItem(row, 2, QTableWidgetItem(customer.phone or ""))
            self.table.setItem(row, 3, QTableWidgetItem(customer.email or ""))
            self.table.setItem(row, 4, QTableWidgetItem(customer.city or ""))

            # Format balance
            balance = float(customer.current_balance or 0)
            balance_item = QTableWidgetItem(f"{balance:,.2f}")
            balance_item.setTextAlignment(Qt.AlignmentFlag.AlignRight | Qt.AlignmentFlag.AlignVCenter)
            self.table.setItem(row, 5, balance_item)

            # Store customer object in first column for easy retrieval
            self.table.item(row, 0).setData(Qt.ItemDataRole.UserRole, customer)
    
    def filter_customers(self):
        """Filter customers based on search text."""
        search_text = self.search_edit.text().lower()

        filtered_customers = []

        for customer in self.all_customers:
            # Text search
            if search_text:
                searchable_text = " ".join([
                    customer.name or "",
                    customer.email or "",
                    customer.contact_person or "",
                    customer.phone or "",
                    customer.mobile or ""
                ]).lower()

                if search_text not in searchable_text:
                    continue

            filtered_customers.append(customer)

        self.populate_table(filtered_customers)
    
    def on_selection_changed(self):
        """Handle table selection change."""
        current_row = self.table.currentRow()
        if current_row >= 0 and current_row < self.table.rowCount():
            try:
                item = self.table.item(current_row, 0)
                if item:
                    customer = item.data(Qt.ItemDataRole.UserRole)
                    if customer:
                        self.customer_selected.emit(customer)
                        return
            except Exception as e:
                logger.error(f"Error in selection change: {e}")

        # If we get here, emit None to clear selection
        self.customer_selected.emit(None)
    
    def refresh(self):
        """Refresh the customer list."""
        self.load_customers()
        self.filter_customers()
    
    def get_selected_customer(self):
        """Get the currently selected customer."""
        current_row = self.table.currentRow()
        if current_row >= 0:
            item = self.table.item(current_row, 0)
            if item:
                return item.data(Qt.ItemDataRole.UserRole)
        return None


class CustomerManagementWidget(QWidget):
    """Main customer management widget combining list and detail views."""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.current_customer = None
        self.setup_ui()

    def setup_ui(self):
        """Set up the main customer management UI."""
        layout = QVBoxLayout(self)

        # Title and toolbar
        title_layout = QHBoxLayout()

        title_label = QLabel("Customer Management")
        title_font = QFont()
        title_font.setPointSize(16)
        title_font.setBold(True)
        title_label.setFont(title_font)

        # Action buttons
        self.add_btn = QPushButton("Add Customer")
        self.add_btn.clicked.connect(self.add_customer)

        self.edit_btn = QPushButton("Edit Customer")
        self.edit_btn.clicked.connect(self.edit_customer)
        self.edit_btn.setEnabled(False)

        self.delete_btn = QPushButton("Delete Customer")
        self.delete_btn.clicked.connect(self.delete_customer)
        self.delete_btn.setEnabled(False)

        self.refresh_btn = QPushButton("Refresh")
        self.refresh_btn.clicked.connect(self.refresh_list)

        title_layout.addWidget(title_label)
        title_layout.addStretch()
        title_layout.addWidget(self.add_btn)
        title_layout.addWidget(self.edit_btn)
        title_layout.addWidget(self.delete_btn)
        title_layout.addWidget(self.refresh_btn)

        layout.addLayout(title_layout)

        # Main content area with splitter
        splitter = QSplitter(Qt.Orientation.Horizontal)

        # Customer list (left side)
        self.customer_list = CustomerListWidget()
        self.customer_list.customer_selected.connect(self.on_customer_selected)
        splitter.addWidget(self.customer_list)

        # Customer details (right side)
        self.details_widget = self.create_details_widget()
        splitter.addWidget(self.details_widget)

        # Set splitter proportions
        splitter.setSizes([600, 400])

        layout.addWidget(splitter)

    def create_details_widget(self):
        """Create customer details display widget."""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # Title
        self.details_title = QLabel("Customer Details")
        title_font = QFont()
        title_font.setPointSize(14)
        title_font.setBold(True)
        self.details_title.setFont(title_font)
        layout.addWidget(self.details_title)

        # Details content in scroll area
        scroll = QScrollArea()
        scroll_widget = QWidget()
        self.details_layout = QVBoxLayout(scroll_widget)

        # Initially show "no customer selected" message
        self.no_selection_label = QLabel("Select a customer from the list to view details")
        self.no_selection_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.no_selection_label.setStyleSheet("color: #666; font-style: italic; padding: 50px;")
        self.details_layout.addWidget(self.no_selection_label)

        scroll.setWidget(scroll_widget)
        scroll.setWidgetResizable(True)
        layout.addWidget(scroll)

        return widget

    def on_customer_selected(self, customer):
        """Handle customer selection."""
        self.current_customer = customer
        if customer:
            self.edit_btn.setEnabled(True)
            self.delete_btn.setEnabled(True)
        else:
            self.edit_btn.setEnabled(False)
            self.delete_btn.setEnabled(False)
        self.show_customer_details(customer)

    def show_customer_details(self, customer):
        """Display customer details in the details panel."""
        # Clear existing details properly
        while self.details_layout.count():
            child = self.details_layout.takeAt(0)
            if child.widget():
                child.widget().deleteLater()

        if not customer:
            self.no_selection_label = QLabel("Select a customer from the list to view details")
            self.no_selection_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            self.no_selection_label.setStyleSheet("color: #666; font-style: italic; padding: 50px;")
            self.details_layout.addWidget(self.no_selection_label)
            return

        # Customer name
        name_label = QLabel(customer.name)
        name_font = QFont()
        name_font.setPointSize(12)
        name_font.setBold(True)
        name_label.setFont(name_font)
        self.details_layout.addWidget(name_label)

        # Add separator
        line = QFrame()
        line.setFrameShape(QFrame.Shape.HLine)
        line.setFrameShadow(QFrame.Shadow.Sunken)
        self.details_layout.addWidget(line)

        # Contact Information
        if any([customer.contact_person, customer.phone, customer.mobile, customer.email]):
            contact_group = QGroupBox("Contact Information")
            contact_layout = QFormLayout(contact_group)

            if customer.contact_person:
                contact_layout.addRow("Contact Person:", QLabel(customer.contact_person))
            if customer.phone:
                contact_layout.addRow("Phone:", QLabel(customer.phone))
            if customer.mobile:
                contact_layout.addRow("Mobile:", QLabel(customer.mobile))
            if customer.email:
                contact_layout.addRow("Email:", QLabel(customer.email))

            self.details_layout.addWidget(contact_group)

        # Address Information
        if customer.full_address.strip():
            address_group = QGroupBox("Address")
            address_layout = QVBoxLayout(address_group)
            address_label = QLabel(customer.full_address)
            address_label.setWordWrap(True)
            address_layout.addWidget(address_label)
            self.details_layout.addWidget(address_group)

        # Financial Information
        financial_group = QGroupBox("Financial Information")
        financial_layout = QFormLayout(financial_group)

        if customer.tax_id:
            financial_layout.addRow("Tax ID:", QLabel(customer.tax_id))

        credit_limit = float(customer.credit_limit or 0)
        financial_layout.addRow("Credit Limit:", QLabel(f"{credit_limit:,.2f}"))

        current_balance = float(customer.current_balance or 0)
        balance_color = "#f44336" if current_balance > 0 else "#4CAF50"
        balance_label = QLabel(f"<span style='color: {balance_color}; font-weight: bold;'>{current_balance:,.2f}</span>")
        financial_layout.addRow("Current Balance:", balance_label)

        self.details_layout.addWidget(financial_group)

        # Notes
        if customer.notes:
            notes_group = QGroupBox("Notes")
            notes_layout = QVBoxLayout(notes_group)
            notes_label = QLabel(customer.notes)
            notes_label.setWordWrap(True)
            notes_layout.addWidget(notes_label)
            self.details_layout.addWidget(notes_group)

        # Add stretch to push content to top
        self.details_layout.addStretch()

    def add_customer(self):
        """Add a new customer."""
        dialog = CustomerForm(parent=self)
        if dialog.exec() == QDialog.DialogCode.Accepted:
            session = None
            try:
                customer_data = dialog.get_customer_data()

                # Validate required fields
                if not customer_data.get('name'):
                    QMessageBox.warning(self, "Validation Error", "Customer name is required.")
                    return

                session = Session()

                # Create new customer
                customer = Customer(**customer_data)
                session.add(customer)
                session.commit()

                QMessageBox.information(self, "Success", "Customer added successfully!")
                self.refresh_list()

            except Exception as e:
                if session:
                    session.rollback()
                logger.error(f"Error adding customer: {e}")
                QMessageBox.critical(self, "Error", f"Failed to add customer: {str(e)}")
            finally:
                if session:
                    session.close()

    def edit_customer(self):
        """Edit the selected customer."""
        if not self.current_customer:
            QMessageBox.warning(self, "No Selection", "Please select a customer to edit.")
            return

        dialog = CustomerForm(customer=self.current_customer, parent=self)
        if dialog.exec() == QDialog.DialogCode.Accepted:
            session = None
            try:
                customer_data = dialog.get_customer_data()

                # Validate required fields
                if not customer_data.get('name'):
                    QMessageBox.warning(self, "Validation Error", "Customer name is required.")
                    return

                session = Session()

                # Update customer
                customer = session.query(Customer).get(self.current_customer.id)
                if not customer:
                    QMessageBox.critical(self, "Error", "Customer not found in database.")
                    return

                for key, value in customer_data.items():
                    setattr(customer, key, value)

                session.commit()

                # Update current customer reference
                self.current_customer = customer

                QMessageBox.information(self, "Success", "Customer updated successfully!")
                self.refresh_list()

                # Refresh the details panel with updated data
                self.show_customer_details(customer)

            except Exception as e:
                if session:
                    session.rollback()
                logger.error(f"Error updating customer: {e}")
                QMessageBox.critical(self, "Error", f"Failed to update customer: {str(e)}")
            finally:
                if session:
                    session.close()

    def delete_customer(self):
        """Delete the selected customer."""
        if not self.current_customer:
            return

        reply = QMessageBox.question(
            self, "Confirm Delete",
            f"Are you sure you want to delete customer '{self.current_customer.name}' ({self.current_customer.code})?\n\n"
            "This action cannot be undone.",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            try:
                session = Session()
                customer = session.query(Customer).get(self.current_customer.id)
                session.delete(customer)
                session.commit()
                session.close()

                QMessageBox.information(self, "Success", "Customer deleted successfully!")
                self.current_customer = None
                self.edit_btn.setEnabled(False)
                self.delete_btn.setEnabled(False)
                self.show_customer_details(None)
                self.refresh_list()

            except Exception as e:
                logger.error(f"Error deleting customer: {e}")
                QMessageBox.critical(self, "Error", f"Failed to delete customer: {str(e)}")

    def refresh_list(self):
        """Refresh the customer list."""
        self.customer_list.refresh()
        # Clear current selection
        self.current_customer = None
        self.edit_btn.setEnabled(False)
        self.delete_btn.setEnabled(False)
        self.show_customer_details(None)
