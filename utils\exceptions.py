"""
Custom exceptions for the application.

This module defines custom exception classes for handling different types of errors
that can occur in the application.
"""
from typing import Any, Dict, Optional, Type, Union

class AppError(Exception):
    """Base class for all application-specific exceptions."""
    
    def __init__(
        self, 
        message: str = "An error occurred",
        code: str = "error",
        status_code: int = 500,
        details: Any = None,
        **kwargs
    ):
        """
        Initialize the exception.
        
        Args:
            message: Human-readable error message
            code: Machine-readable error code
            status_code: HTTP status code (if applicable)
            details: Additional error details
            **kwargs: Additional attributes to set on the exception
        """
        self.message = message
        self.code = code
        self.status_code = status_code
        self.details = details
        
        # Set additional attributes
        for key, value in kwargs.items():
            setattr(self, key, value)
        
        super().__init__(self.message)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert the exception to a dictionary."""
        result = {
            'code': self.code,
            'message': self.message,
        }
        
        if self.details is not None:
            result['details'] = self.details
        
        return result


class ValidationError(AppError):
    """Raised when validation of input data fails."""
    
    def __init__(
        self, 
        message: str = "Validation failed",
        errors: Optional[Dict[str, Any]] = None,
        **kwargs
    ):
        """
        Initialize the validation error.
        
        Args:
            message: Error message
            errors: Dictionary of field errors
            **kwargs: Additional attributes
        """
        super().__init__(
            message=message,
            code="validation_error",
            status_code=400,
            details=errors or {},
            **kwargs
        )


class NotFoundError(AppError):
    """Raised when a requested resource is not found."""
    
    def __init__(
        self, 
        resource: str = "resource",
        resource_id: Any = None,
        **kwargs
    ):
        """
        Initialize the not found error.
        
        Args:
            resource: Name of the resource that was not found
            resource_id: ID of the resource that was not found
            **kwargs: Additional attributes
        """
        message = f"{resource.capitalize()} not found"
        if resource_id is not None:
            message = f"{resource.capitalize()} with ID {resource_id} not found"
        
        super().__init__(
            message=message,
            code="not_found",
            status_code=404,
            resource=resource,
            resource_id=resource_id,
            **kwargs
        )


class AuthenticationError(AppError):
    """Raised when authentication fails."""
    
    def __init__(
        self, 
        message: str = "Authentication failed",
        **kwargs
    ):
        """
        Initialize the authentication error.
        
        Args:
            message: Error message
            **kwargs: Additional attributes
        """
        super().__init__(
            message=message,
            code="authentication_error",
            status_code=401,
            **kwargs
        )


class AuthorizationError(AppError):
    """Raised when a user is not authorized to perform an action."""
    
    def __init__(
        self, 
        message: str = "Not authorized",
        **kwargs
    ):
        """
        Initialize the authorization error.
        
        Args:
            message: Error message
            **kwargs: Additional attributes
        """
        super().__init__(
            message=message,
            code="authorization_error",
            status_code=403,
            **kwargs
        )


class DatabaseError(AppError):
    """Raised when a database operation fails."""
    
    def __init__(
        self, 
        message: str = "Database operation failed",
        **kwargs
    ):
        """
        Initialize the database error.
        
        Args:
            message: Error message
            **kwargs: Additional attributes
        """
        super().__init__(
            message=message,
            code="database_error",
            status_code=500,
            **kwargs
        )


class IntegrityError(DatabaseError):
    """Raised when a database integrity constraint is violated."""
    
    def __init__(
        self, 
        message: str = "Database integrity error",
        constraint: str = None,
        **kwargs
    ):
        """
        Initialize the integrity error.
        
        Args:
            message: Error message
            constraint: Name of the constraint that was violated
            **kwargs: Additional attributes
        """
        if constraint:
            message = f"Database integrity error: {constraint} constraint violation"
            
        super().__init__(
            message=message,
            code="integrity_error",
            constraint=constraint,
            **kwargs
        )


def handle_error(
    error: Exception,
    default_message: str = "An unexpected error occurred",
    logger=None
) -> AppError:
    """
    Handle an exception and return an appropriate AppError.
    
    Args:
        error: The exception to handle
        default_message: Default message to use if the error is not recognized
        logger: Optional logger instance for logging the error
        
    Returns:
        An AppError instance representing the error
    """
    if logger is not None:
        logger.exception(error)
    
    if isinstance(error, AppError):
        return error
    
    # Map common exception types to AppError subclasses
    error_map = {
        'IntegrityError': IntegrityError,
        'DataError': DatabaseError,
        'DatabaseError': DatabaseError,
        'OperationalError': DatabaseError,
        'ProgrammingError': DatabaseError,
        'NotSupportedError': DatabaseError,
    }
    
    error_class = error.__class__.__name__
    error_message = str(error) or default_message
    
    if error_class in error_map:
        return error_map[error_class](error_message)
    
    # Default to a generic application error
    return AppError(message=error_message)
