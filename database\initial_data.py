import sys
from pathlib import Path
from datetime import datetime, timedelta
from sqlalchemy.orm import Session

# Add the project root to the Python path
sys.path.append(str(Path(__file__).parent.parent))

from models.user import User
from models.customer import Customer
from models.product import Product, ProductCategory
from models.invoice import Invoice, InvoiceLine, InvoiceStatus, PaymentMethod
from database.database import SessionLocal, engine, Base

def create_initial_data():
    """Create initial test data for the application."""
    db = SessionLocal()
    
    try:
        # Create admin user if not exists
        admin = db.query(User).filter(User.username == "admin").first()
        if not admin:
            admin = User(
                username="admin",
                email="<EMAIL>",
                hashed_password="$2b$12$EixZaYVK1fsbw1ZfbX3OXePaWxn96p36WQoeG6Lruj3vjPGga31lW",  # password: admin
                full_name="Admin User",
                is_admin=True,
                is_active=True
            )
            db.add(admin)
            db.commit()
            print("Created admin user")
        
        # Create product categories
        categories = [
            ProductCategory(name="Electronics", description="Electronic devices and accessories"),
            ProductCategory(name="Office Supplies", description="Office and stationery items"),
            ProductCategory(name="Furniture", description="Office furniture and equipment")
        ]
        
        for category in categories:
            if not db.query(ProductCategory).filter(ProductCategory.name == category.name).first():
                db.add(category)
        
        db.commit()
        print("Created product categories")
        
        # Create some products
        products = [
            {
                "code": "LAP-001",
                "name": "Laptop Computer",
                "description": "High-performance laptop",
                "purchase_price": 800.00,
                "selling_price": 1200.00,
                "tax_rate": 15.0,
                "quantity_in_stock": 10,
                "category_name": "Electronics"
            },
            {
                "code": "DESK-001",
                "name": "Office Desk",
                "description": "Standard office desk",
                "purchase_price": 150.00,
                "selling_price": 250.00,
                "tax_rate": 10.0,
                "quantity_in_stock": 15,
                "category_name": "Furniture"
            },
            {
                "code": "PEN-001",
                "name": "Ballpoint Pen",
                "description": "Black ballpoint pen, pack of 10",
                "purchase_price": 2.50,
                "selling_price": 5.00,
                "tax_rate": 5.0,
                "quantity_in_stock": 100,
                "category_name": "Office Supplies"
            }
        ]
        
        for product_data in products:
            if not db.query(Product).filter(Product.code == product_data["code"]).first():
                category = db.query(ProductCategory).filter(
                    ProductCategory.name == product_data.pop("category_name")
                ).first()
                
                product = Product(**product_data)
                product.category = category
                db.add(product)
        
        db.commit()
        print("Created products")
        
        # Create some customers
        customers = [
            {
                "name": "ABC Corporation",
                "code": "CUST-001",
                "contact_person": "John Doe",
                "phone": "+1234567890",
                "email": "<EMAIL>",
                "address": "123 Business St, Downtown",
                "city": "Riyadh",
                "country": "Saudi Arabia",
                "tax_id": "1234567890"
            },
            {
                "name": "XYZ Trading",
                "code": "CUST-002",
                "contact_person": "Jane Smith",
                "phone": "+1987654321",
                "email": "<EMAIL>",
                "address": "456 Trade Ave, Industrial Area",
                "city": "Jeddah",
                "country": "Saudi Arabia",
                "tax_id": "0987654321"
            }
        ]
        
        for customer_data in customers:
            if not db.query(Customer).filter(Customer.code == customer_data["code"]).first():
                customer = Customer(**customer_data)
                db.add(customer)
        
        db.commit()
        print("Created customers")
        
        # Create a sample invoice
        customer = db.query(Customer).filter(Customer.code == "CUST-001").first()
        if customer:
            invoice = Invoice(
                invoice_number="INV-2023-0001",
                invoice_date=datetime.utcnow(),
                due_date=datetime.utcnow() + timedelta(days=30),
                status=InvoiceStatus.PAID,
                is_paid=True,
                payment_method=PaymentMethod.BANK_TRANSFER,
                payment_date=datetime.utcnow(),
                payment_reference="BANK-REF-12345",
                customer_id=customer.id,
                user_id=admin.id
            )
            
            # Add invoice lines
            laptop = db.query(Product).filter(Product.code == "LAP-001").first()
            pen = db.query(Product).filter(Product.code == "PEN-001").first()
            
            if laptop and pen:
                lines = [
                    InvoiceLine(
                        product_id=laptop.id,
                        description=laptop.name,
                        quantity=2,
                        unit_price=laptop.selling_price,
                        tax_rate=laptop.tax_rate
                    ),
                    InvoiceLine(
                        product_id=pen.id,
                        description=pen.name,
                        quantity=5,
                        unit_price=pen.selling_price,
                        tax_rate=pen.tax_rate
                    )
                ]
                
                invoice.lines = lines
                invoice.calculate_totals()
                
                db.add(invoice)
                db.commit()
                print("Created sample invoice")
        
        print("Database initialization complete!")
        
    except Exception as e:
        db.rollback()
        print(f"Error initializing database: {e}")
        raise
    finally:
        db.close()

if __name__ == "__main__":
    # Create all tables
    from database.database import Base
    Base.metadata.create_all(bind=engine)
    
    # Create initial data
    create_initial_data()
