"""
String utility functions.

This module provides utility functions for working with strings,
including formatting, validation, and manipulation.
"""
import re
import unicodedata
from typing import Any, Dict, List, Optional, Tuple, Union
from datetime import datetime

from .exceptions import AppError
from .datetime_utils import parse_datetime, format_datetime

def is_blank(s: Optional[str]) -> bool:
    """
    Check if a string is None, empty, or contains only whitespace.
    
    Args:
        s: String to check
        
    Returns:
        True if the string is blank, False otherwise
    """
    return s is None or not s.strip()

def is_empty(s: Optional[str]) -> bool:
    """
    Check if a string is None or empty.
    
    Args:
        s: String to check
        
    Returns:
        True if the string is None or empty, False otherwise
    """
    return s is None or s == ""

def to_str(value: Any, default: str = "") -> str:
    """
    Convert a value to a string, handling None and empty values.
    
    Args:
        value: Value to convert
        default: Default value to return if conversion fails
        
    Returns:
        String representation of the value, or the default value
    """
    if value is None:
        return default
    
    try:
        # Handle datetime objects
        if isinstance(value, (datetime, datetime.date, datetime.time)):
            return format_datetime(value)
        
        # Handle bytes
        if isinstance(value, bytes):
            return value.decode('utf-8', errors='replace')
        
        # Convert to string
        return str(value)
    except Exception:
        return default

def to_int(value: Any, default: int = 0) -> int:
    """
    Convert a value to an integer, handling None and invalid values.
    
    Args:
        value: Value to convert
        default: Default value to return if conversion fails
        
    Returns:
        Integer value, or the default value
    """
    if value is None:
        return default
    
    try:
        # Handle boolean values
        if isinstance(value, bool):
            return 1 if value else 0
        
        # Handle numeric strings with thousand separators
        if isinstance(value, str):
            value = value.replace(',', '').strip()
        
        return int(float(value))
    except (ValueError, TypeError):
        return default

def to_float(value: Any, default: float = 0.0) -> float:
    """
    Convert a value to a float, handling None and invalid values.
    
    Args:
        value: Value to convert
        default: Default value to return if conversion fails
        
    Returns:
        Float value, or the default value
    """
    if value is None:
        return default
    
    try:
        # Handle boolean values
        if isinstance(value, bool):
            return 1.0 if value else 0.0
        
        # Handle numeric strings with thousand separators
        if isinstance(value, str):
            value = value.replace(',', '').strip()
        
        return float(value)
    except (ValueError, TypeError):
        return default

def to_bool(value: Any, default: bool = False) -> bool:
    """
    Convert a value to a boolean, handling various string representations.
    
    Args:
        value: Value to convert
        default: Default value to return if conversion fails
        
    Returns:
        Boolean value, or the default value
    """
    if value is None:
        return default
    
    if isinstance(value, bool):
        return value
    
    if isinstance(value, (int, float)):
        return bool(value)
    
    if not isinstance(value, str):
        return default
    
    value = value.strip().lower()
    
    if value in ('true', 't', 'yes', 'y', '1', 'on'):
        return True
    
    if value in ('false', 'f', 'no', 'n', '0', 'off'):
        return False
    
    return default

def truncate(text: str, max_length: int, ellipsis: str = "...") -> str:
    """
    Truncate a string to a maximum length, adding an ellipsis if truncated.
    
    Args:
        text: Text to truncate
        max_length: Maximum length of the resulting string (including ellipsis)
        ellipsis: Ellipsis string to append if text is truncated
        
    Returns:
        Truncated string with ellipsis if necessary
    """
    if not text or max_length <= 0:
        return ""
    
    if len(text) <= max_length:
        return text
    
    if len(ellipsis) >= max_length:
        return ellipsis[:max_length]
    
    return text[:max_length - len(ellipsis)] + ellipsis

def slugify(text: str, separator: str = '-', lowercase: bool = True) -> str:
    """
    Convert a string to a URL-friendly slug.
    
    Args:
        text: Text to convert to a slug
        separator: Word separator (default: '-')
        lowercase: Whether to convert to lowercase (default: True)
        
    Returns:
        URL-friendly slug
    """
    if not text:
        return ""
    
    # Normalize unicode characters
    text = unicodedata.normalize('NFKD', str(text))
    
    # Convert to lowercase if requested
    if lowercase:
        text = text.lower()
    
    # Replace non-alphanumeric and non-whitespace with separator
    text = re.sub(r'[^\w\s-]', '', text).strip()
    
    # Replace whitespace and multiple separators with a single separator
    text = re.sub(r'[\s-]+', separator, text)
    
    # Remove leading/trailing separators
    text = text.strip(separator)
    
    return text

def format_currency(
    amount: Union[int, float, str],
    currency: str = "USD",
    symbol: bool = True,
    decimals: int = 2,
    decimal_separator: str = ".",
    thousand_separator: str = ","
) -> str:
    """
    Format a number as a currency string.
    
    Args:
        amount: Amount to format
        currency: Currency code (e.g., 'USD', 'EUR')
        symbol: Whether to include the currency symbol
        decimals: Number of decimal places to show
        decimal_separator: Decimal separator (default: '.')
        thousand_separator: Thousands separator (default: ',')
        
    Returns:
        Formatted currency string
    """
    try:
        # Convert to float and round
        amount_float = float(amount)
        amount_rounded = round(amount_float, decimals)
        
        # Format the number with separators
        format_str = ",.{}f".format(decimals)
        parts = "{0:{1}}".format(amount_rounded, format_str).split(".")
        
        # Add thousand separators
        integer_part = parts[0].replace(",", thousand_separator)
        
        # Combine integer and decimal parts
        if len(parts) > 1:
            formatted = f"{integer_part}{decimal_separator}{parts[1]}"
        else:
            formatted = integer_part
        
        # Add currency symbol
        if symbol:
            symbols = {
                'USD': '$", '
                'EUR': '€',
                'GBP': '£',
                'JPY': '¥',
                'CNY': '¥',
                'INR': '₹',
                'RUB': '₽',
                'BRL': 'R$',
                'MXN': 'MX$',
                'KRW': '₩',
                'TRY': '₺',
                'SAR': '﷼',
                'AED': 'د.إ',
                'EGP': 'E£',
                'ZAR': 'R',
                'AUD': 'A$',
                'CAD': 'C$',
                'NZD': 'NZ$',
                'CHF': 'CHF',
                'SEK': 'kr',
                'NOK': 'kr',
                'DKK': 'kr',
                'PLN': 'zł',
                'HUF': 'Ft',
                'CZK': 'Kč',
                'ILS': '₪',
                'THB': '฿',
                'IDR': 'Rp',
                'MYR': 'RM',
                'PHP': '₱',
                'SGD': 'S$',
                'TWD': 'NT$',
                'HKD': 'HK$',
                'VND': '₫',
                'BDT': '৳',
                'PKR': '₨',
                'LKR': '₨',
                'NGN': '₦',
                'KES': 'KSh',
                'GHS': 'GH₵',
                'ETB': 'Br',
                'MAD': 'د.م.',
                'DZD': 'د.ج',
                'TND': 'د.ت',
                'JOD': 'د.أ',
                'LBP': 'ل.ل',
                'QAR': 'ر.ق',
                'KWD': 'د.ك',
                'BHD': 'د.ب',
                'OMR': 'ر.ع.',
                'YER': '﷼',
                'AFN': '؋',
                'NPR': 'रू',
                'MMK': 'K',
                'KHR': '៛',
                'LAK': '₭',
                'MNT': '₮',
                'KZT': '₸',
                'UZS': 'so\'m',
                'AZN': '₼',
                'GEL': '₾',
                'AMD': '֏',
                'ALL': 'L',
                'BAM': 'KM',
                'BGN': 'лв',
                'HRK': 'kn',
                'MKD': 'ден',
                'MDL': 'L',
                'RON': 'lei',
                'RSD': 'дин',
                'UAH': '₴',
                'BYN': 'Br',
                'ISK': 'kr',
                'FJD': 'FJ$',
                'XPF': '₣',
                'XAF': 'FCFA',
                'XOF': 'CFA',
                'XCD': 'EC$',
                'ANG': 'ƒ',
                'AWG': 'ƒ',
                'BBD': 'Bds$',
                'BMD': 'BD$',
                'BND': 'B$',
                'BSD': 'B$',
                'BZD': 'BZ$',
                'CUC': 'CUC$',
                'CUP': '$MN',
                'DOP': 'RD$',
                'FJD': 'FJ$',
                'GIP': '£',
                'GTQ': 'Q',
                'GYD': 'G$',
                'HNL': 'L',
                'JMD': 'J$',
                'KYD': 'CI$',
                'LRD': 'L$',
                'NAD': 'N$',
                'SBD': 'SI$',
                'SHP': '£',
                'SRD': '$',
                'TTD': 'TT$',
                'TVD': '$T',
                'VEF': 'Bs.',
                'XCD': 'EC$',
                'ZWD': 'Z$',
                'ZWL': 'Z$',
            }
            
            symbol = symbols.get(currency.upper(), currency.upper())
            formatted = f"{symbol}{formatted}"
        
        return formatted
    except (ValueError, TypeError):
        return str(amount)

def parse_currency(
    value: str,
    currency: str = "USD",
    decimal_separator: str = "."
) -> float:
    """
    Parse a currency string into a float.
    
    Args:
        value: Currency string to parse
        currency: Currency code (used to determine symbol position)
        decimal_separator: Expected decimal separator
        
    Returns:
        Parsed float value
        
    Raises:
        AppError: If the string cannot be parsed as a currency
    """
    if not value:
        return 0.0
    
    try:
        # Remove all non-numeric characters except decimal separator and minus sign
        pattern = f"[^0-9{re.escape(decimal_separator)}-]"
        cleaned = re.sub(pattern, "", str(value))
        
        # Handle negative numbers
        is_negative = "-" in cleaned
        cleaned = cleaned.replace("-", "")
        
        # Handle different decimal separators
        if decimal_separator != ".":
            cleaned = cleaned.replace(decimal_separator, ".")
        
        # Convert to float
        result = float(cleaned)
        
        # Apply negative sign if needed
        if is_negative:
            result = -result
        
        return result
    except (ValueError, TypeError) as e:
        raise AppError(f"Invalid currency format: {value}") from e

def pluralize(
    count: int,
    singular: str,
    plural: Optional[str] = None,
    include_count: bool = True
) -> str:
    """
    Return the singular or plural form of a word based on a count.
    
    Args:
        count: The count to base the pluralization on
        singular: The singular form of the word
        plural: The plural form of the word (default: singular + 's')
        include_count: Whether to include the count in the result
        
    Returns:
        The appropriate form of the word, optionally prefixed by the count
    """
    if plural is None:
        plural = f"{singular}s"
    
    word = singular if count == 1 else plural
    
    if include_count:
        return f"{count:,} {word}"
    
    return word

def format_phone_number(
    number: str,
    format_str: str = "(###) ###-####"
) -> str:
    """
    Format a phone number according to the specified format.
    
    Args:
        number: The phone number to format (digits only)
        format_str: The format string using '#' as digit placeholders
                   (default: "(###) ###-####")
                   
    Returns:
        Formatted phone number
    """
    if not number:
        return ""
    
    # Remove all non-digit characters
    digits = re.sub(r"[^0-9]", "", str(number))
    
    if not digits:
        return ""
    
    result = []
    digit_index = 0
    
    for char in format_str:
        if char == '#':
            if digit_index < len(digits):
                result.append(digits[digit_index])
                digit_index += 1
            else:
                break
        else:
            result.append(char)
    
    return "".join(result)

def mask_string(
    text: str,
    visible_chars: int = 4,
    mask_char: str = "*",
    from_start: bool = False
) -> str:
    """
    Mask part of a string, showing only a specified number of characters.
    
    Args:
        text: The string to mask
        visible_chars: Number of characters to keep visible
        mask_char: Character to use for masking
        from_start: If True, show the first N characters; if False, show the last N
        
    Returns:
        Masked string
    """
    if not text:
        return ""
    
    text = str(text)
    text_len = len(text)
    
    if visible_chars >= text_len:
        return text
    
    if from_start:
        visible_part = text[:visible_chars]
        masked_part = mask_char * (text_len - visible_chars)
        return f"{visible_part}{masked_part}"
    else:
        visible_part = text[-visible_chars:]
        masked_part = mask_char * (text_len - visible_chars)
        return f"{masked_part}{visible_part}"
