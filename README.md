# Rabie Al-Khaleej Accounting Application

A comprehensive accounting and business management solution designed to replace the existing Microsoft Access application.

## Features

- **User Management**: Secure authentication with role-based access control
- **Customer Management**: Track customer information, contacts, and interactions
- **Product Catalog**: Manage inventory with categories and stock levels
- **Invoicing**: Create, edit, and track customer invoices
- **Financial Tracking**: Monitor payments, expenses, and financial reports
- **Modern UI**: Intuitive PyQt6-based desktop interface
- **Database**: SQLite database with SQLAlchemy ORM (compatible with other databases)

## Quick Start

### Prerequisites

- Python 3.8 or higher
- pip (Python package manager)
- Git (optional, for version control)

### Installation

1. **Clone the repository** (or download and extract the ZIP file)
   ```bash
   git clone <repository-url>
   cd RabieAlKhaleejApp
   ```

2. **Set up a virtual environment** (recommended)
   ```bash
   # Windows
   python -m venv venv
   venv\Scripts\activate
   
   # macOS/Linux
   python3 -m venv venv
   source venv/bin/activate
   ```

3. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

4. **Configure the application**
   - Copy `.env.example` to `.env`
   - Edit `.env` to configure database and other settings

5. **Initialize the database**
   ```bash
   python scripts/init_db.py
   ```

6. **Run the application**
   ```bash
   python run.py
   ```

## Default Login Credentials

- **Username**: admin
- **Password**: admin

## Project Structure

```bash
python main.py
```

## Development

### Project Structure

```
RabieAlKhaleejApp/
├── config/           # Configuration settings
├── database/         # Database models and migrations
├── modules/          # Application modules
├── ui/               # User interface components
├── utils/            # Utility functions
├── main.py           # Application entry point
└── requirements.txt  # Python dependencies
```

### Code Style

This project follows the [PEP 8](https://www.python.org/dev/peps/pep-0008/) style guide.

### Testing

To run the test suite:

```bash
python -m pytest
```

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Acknowledgments

- Built with [Python](https://www.python.org/) and [PyQt6](https://www.riverbankcomputing.com/software/pyqt/)
- Database powered by [SQLAlchemy](https://www.sqlalchemy.org/)
- Icons by [Material Design Icons](https://material.io/resources/icons/)
