"""
File utility functions.

This module provides utility functions for common file operations,
including reading, writing, and manipulating files and directories.
"""
import os
import shutil
import tempfile
import hashlib
import mimetypes
import zipfile
from pathlib import Path
from typing import Union, BinaryIO, Optional, List, Dict, Any, Tuple
from datetime import datetime

from .exceptions import AppError

# Default chunk size for file operations (64KB)
DEFAULT_CHUNK_SIZE = 64 * 1024

def ensure_directory_exists(directory: Union[str, Path]) -> Path:
    """
    Ensure that a directory exists, creating it if necessary.
    
    Args:
        directory: Path to the directory
        
    Returns:
        Path object for the directory
        
    Raises:
        AppError: If the directory cannot be created or is not writable
    """
    try:
        path = Path(directory).resolve()
        path.mkdir(parents=True, exist_ok=True)
        
        # Verify write permission
        test_file = path / f".test_{os.urandom(8).hex()}"
        try:
            test_file.touch()
            test_file.unlink()
        except OSError as e:
            raise AppError(f"Directory is not writable: {path}") from e
            
        return path
    except Exception as e:
        raise AppError(f"Failed to create directory {directory}: {e}")


def get_file_hash(
    file_path: Union[str, Path],
    algorithm: str = 'sha256',
    chunk_size: int = DEFAULT_CHUNK_SIZE
) -> str:
    """
    Calculate the hash of a file.
    
    Args:
        file_path: Path to the file
        algorithm: Hash algorithm to use (e.g., 'md5', 'sha1', 'sha256')
        chunk_size: Size of chunks to read at a time
        
    Returns:
        Hexadecimal digest of the file
        
    Raises:
        AppError: If the file cannot be read
    """
    file_path = Path(file_path)
    
    if not file_path.is_file():
        raise AppError(f"File not found: {file_path}")
    
    try:
        hash_func = hashlib.new(algorithm)
        
        with open(file_path, 'rb') as f:
            for chunk in iter(lambda: f.read(chunk_size), b''):
                hash_func.update(chunk)
                
        return hash_func.hexdigest()
    except Exception as e:
        raise AppError(f"Failed to calculate hash for {file_path}: {e}")


def get_file_info(file_path: Union[str, Path]) -> Dict[str, Any]:
    """
    Get information about a file.
    
    Args:
        file_path: Path to the file
        
    Returns:
        Dictionary containing file information
        
    Raises:
        AppError: If the file cannot be accessed
    """
    file_path = Path(file_path)
    
    try:
        stat = file_path.stat()
        mime_type, encoding = mimetypes.guess_type(file_path)
        
        return {
            'path': str(file_path),
            'name': file_path.name,
            'size': stat.st_size,
            'created': datetime.fromtimestamp(stat.st_ctime),
            'modified': datetime.fromtimestamp(stat.st_mtime),
            'accessed': datetime.fromtimestamp(stat.st_atime),
            'mime_type': mime_type,
            'encoding': encoding,
            'is_dir': file_path.is_dir(),
            'is_file': file_path.is_file(),
            'is_symlink': file_path.is_symlink(),
            'parent': str(file_path.parent),
            'suffix': file_path.suffix,
            'suffixes': file_path.suffixes,
            'stem': file_path.stem,
        }
    except Exception as e:
        raise AppError(f"Failed to get file info for {file_path}: {e}")


def copy_file(
    source: Union[str, Path],
    destination: Union[str, Path],
    overwrite: bool = False,
    preserve_metadata: bool = True
) -> Path:
    """
    Copy a file to a new location.
    
    Args:
        source: Path to the source file
        destination: Path to the destination file or directory
        overwrite: Whether to overwrite an existing file
        preserve_metadata: Whether to preserve file metadata (timestamps, permissions)
        
    Returns:
        Path to the copied file
        
    Raises:
        AppError: If the copy operation fails
    """
    source = Path(source)
    destination = Path(destination)
    
    if not source.is_file():
        raise AppError(f"Source file not found: {source}")
    
    # If destination is a directory, use the source filename
    if destination.is_dir():
        destination = destination / source.name
    
    # Check if destination exists
    if destination.exists() and not overwrite:
        raise AppError(f"Destination file already exists: {destination}")
    
    try:
        # Ensure parent directory exists
        ensure_directory_exists(destination.parent)
        
        # Copy the file
        if preserve_metadata:
            shutil.copy2(source, destination)
        else:
            shutil.copy(source, destination)
            
        return destination
    except Exception as e:
        raise AppError(f"Failed to copy {source} to {destination}: {e}")


def move_file(
    source: Union[str, Path],
    destination: Union[str, Path],
    overwrite: bool = False
) -> Path:
    """
    Move a file to a new location.
    
    Args:
        source: Path to the source file
        destination: Path to the destination file or directory
        overwrite: Whether to overwrite an existing file
        
    Returns:
        Path to the moved file
        
    Raises:
        AppError: If the move operation fails
    """
    source = Path(source)
    destination = Path(destination)
    
    if not source.is_file():
        raise AppError(f"Source file not found: {source}")
    
    # If destination is a directory, use the source filename
    if destination.is_dir():
        destination = destination / source.name
    
    # Check if destination exists
    if destination.exists():
        if not overwrite:
            raise AppError(f"Destination file already exists: {destination}")
        destination.unlink()  # Remove existing file if overwrite is True
    
    try:
        # Ensure parent directory exists
        ensure_directory_exists(destination.parent)
        
        # Move the file
        shutil.move(str(source), str(destination))
        return destination
    except Exception as e:
        raise AppError(f"Failed to move {source} to {destination}: {e}")


def delete_file(file_path: Union[str, Path], missing_ok: bool = False) -> None:
    """
    Delete a file.
    
    Args:
        file_path: Path to the file to delete
        missing_ok: If True, no error is raised if the file doesn't exist
        
    Raises:
        AppError: If the file cannot be deleted
    """
    file_path = Path(file_path)
    
    if not file_path.exists():
        if not missing_ok:
            raise AppError(f"File not found: {file_path}")
        return
    
    try:
        file_path.unlink()
    except Exception as e:
        raise AppError(f"Failed to delete {file_path}: {e}")


def create_temp_file(
    content: Optional[bytes] = None,
    suffix: str = '',
    prefix: str = 'tmp',
    directory: Optional[Union[str, Path]] = None,
    delete: bool = True
) -> Tuple[Path, BinaryIO]:
    """
    Create a temporary file.
    
    Args:
        content: Optional content to write to the file
        suffix: File suffix (e.g., '.txt')
        prefix: File prefix
        directory: Directory to create the file in (default: system temp dir)
        delete: Whether to delete the file when closed
        
    Returns:
        A tuple of (file_path, file_object)
        
    Raises:
        AppError: If the file cannot be created
    """
    try:
        # Ensure the directory exists
        if directory is not None:
            directory = ensure_directory_exists(directory)
        
        # Create a named temporary file
        file_obj = tempfile.NamedTemporaryFile(
            mode='w+b',
            prefix=prefix,
            suffix=suffix,
            dir=str(directory) if directory else None,
            delete=delete
        )
        
        # Write content if provided
        if content is not None:
            file_obj.write(content)
            file_obj.flush()
        
        # Get the file path
        file_path = Path(file_obj.name)
        
        return file_path, file_obj
    except Exception as e:
        raise AppError(f"Failed to create temporary file: {e}")


def create_zip_archive(
    source_dir: Union[str, Path],
    output_path: Optional[Union[str, Path]] = None,
    include_hidden: bool = False
) -> Path:
    """
    Create a ZIP archive of a directory.
    
    Args:
        source_dir: Directory to archive
        output_path: Path to the output ZIP file (default: source_dir with .zip)
        include_hidden: Whether to include hidden files (starting with '.')
        
    Returns:
        Path to the created ZIP file
        
    Raises:
        AppError: If the archive cannot be created
    """
    source_dir = Path(source_dir).resolve()
    
    if not source_dir.is_dir():
        raise AppError(f"Source directory not found: {source_dir}")
    
    if output_path is None:
        output_path = source_dir.with_suffix('.zip')
    else:
        output_path = Path(output_path)
    
    try:
        with zipfile.ZipFile(
            output_path, 'w', zipfile.ZIP_DEFLATED, compresslevel=6
        ) as zipf:
            for file_path in source_dir.rglob('*'):
                # Skip hidden files if not included
                if not include_hidden and any(
                    part.startswith('.') for part in file_path.parts
                ):
                    continue
                
                # Add the file to the archive
                if file_path.is_file():
                    arcname = file_path.relative_to(source_dir)
                    zipf.write(file_path, arcname)
        
        return output_path
    except Exception as e:
        if output_path.exists():
            output_path.unlink()
        raise AppError(f"Failed to create ZIP archive: {e}")
