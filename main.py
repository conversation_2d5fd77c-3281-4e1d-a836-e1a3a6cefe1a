#!/usr/bin/env python3
"""
<PERSON><PERSON> Accounting Application

A comprehensive accounting and business management solution.
"""
import sys
import os
import traceback
import logging
from pathlib import Path

# Set up basic logging
log_file = Path(__file__).parent / 'app.log'
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def log_unhandled_exception(exc_type, exc_value, exc_traceback):
    """Log unhandled exceptions."""
    if issubclass(exc_type, KeyboardInterrupt):
        # Call the default excepthook for keyboard interrupts
        sys.__excepthook__(exc_type, exc_value, exc_traceback)
        return
    
    logger.critical("Unhandled exception", exc_info=(exc_type, exc_value, exc_traceback))
    print(f"\nA critical error occurred. Please check the log file: {log_file}")

# Set the exception hook
sys.excepthook = log_unhandled_exception

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def main():
    """Main entry point for the application."""
    try:
        logger.info("Starting application initialization")
        
        # Import required modules
        try:
            from PyQt6.QtWidgets import QApplication, QMessageBox
            from ui.main_window import MainWindow
            from config import settings
            from database.session import init_db
            
            logger.info("Initializing database...")
            try:
                init_db()
                logger.info("Database initialized successfully")
            except Exception as e:
                logger.error(f"Failed to initialize database: {e}", exc_info=True)
                raise
            
            logger.info("Creating application instance")
            app = QApplication(sys.argv)
            app.setApplicationName(settings.APP_NAME)
            app.setApplicationVersion(settings.VERSION)
            
            logger.info("Creating main window")
            window = MainWindow()
            window.show()
            
            logger.info("Starting application event loop")
            return app.exec()
            
        except ImportError as e:
            logger.error(f"Import error: {e}", exc_info=True)
            print(f"Error: {e}")
            print("\nPlease install the required dependencies using:")
            print("pip install -r requirements.txt")
            return 1
            
    except Exception as e:
        logger.critical(f"Fatal error: {e}", exc_info=True)
        print(f"\nA fatal error occurred. Please check the log file: {log_file}")
        
        # Show error message if possible
        try:
            app = QApplication.instance() or QApplication(sys.argv)
            msg = QMessageBox()
            msg.setIcon(QMessageBox.Icon.Critical)
            msg.setText("A critical error occurred")
            msg.setInformativeText(str(e))
            msg.setWindowTitle("Error")
            msg.exec()
        except:
            pass
            
        return 1

if __name__ == "__main__":
    sys.exit(main())
