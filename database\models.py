from datetime import datetime
from sqlalchemy import Column, Integer, String, Float, Date, DateTime, ForeignKey, Boolean
from sqlalchemy.orm import relationship
from .connection import Base

class BaseModel:
    """Base model with common fields and methods."""
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    is_active = Column(Boolean, default=True)

class User(Base, BaseModel):
    """User model for authentication and authorization."""
    __tablename__ = 'users'
    
    id = Column(Integer, primary_key=True, index=True)
    username = Column(String(50), unique=True, nullable=False)
    email = Column(String(100), unique=True, nullable=False)
    hashed_password = Column(String(255), nullable=False)
    full_name = Column(String(100))
    role = Column(String(20), default='user')  # admin, accountant, clerk, etc.
    
    # Relationships
    created_invoices = relationship("Invoice", back_populates="created_by_user")

class Account(Base, BaseModel):
    """Chart of Accounts model."""
    __tablename__ = 'accounts'
    
    id = Column(Integer, primary_key=True, index=True)
    account_code = Column(String(20), unique=True, nullable=False)
    account_name = Column(String(100), nullable=False)
    account_type = Column(String(50))  # Asset, Liability, Equity, Revenue, Expense
    parent_id = Column(Integer, ForeignKey('accounts.id'), nullable=True)
    balance = Column(Float, default=0.0)
    is_active = Column(Boolean, default=True)
    
    # Relationships
    parent = relationship("Account", remote_side=[id], back_populates="children")
    children = relationship("Account", back_populates="parent")
    debit_entries = relationship("JournalEntryLine", foreign_keys="JournalEntryLine.debit_account_id", back_populates="debit_account")
    credit_entries = relationship("JournalEntryLine", foreign_keys="JournalEntryLine.credit_account_id", back_populates="credit_account")

class JournalEntry(Base, BaseModel):
    """Journal Entry model."""
    __tablename__ = 'journal_entries'
    
    id = Column(Integer, primary_key=True, index=True)
    entry_number = Column(String(20), unique=True, nullable=False)
    entry_date = Column(Date, nullable=False)
    reference = Column(String(100))
    description = Column(String(255))
    status = Column(String(20), default='draft')  # draft, posted, cancelled
    
    # Relationships
    lines = relationship("JournalEntryLine", back_populates="journal_entry")
    created_by = Column(Integer, ForeignKey('users.id'))
    created_by_user = relationship("User", back_populates="created_entries")

class JournalEntryLine(Base):
    """Journal Entry Line model."""
    __tablename__ = 'journal_entry_lines'
    
    id = Column(Integer, primary_key=True, index=True)
    journal_entry_id = Column(Integer, ForeignKey('journal_entries.id'), nullable=False)
    line_number = Column(Integer, nullable=False)
    account_id = Column(Integer, ForeignKey('accounts.id'), nullable=False)
    debit = Column(Float, default=0.0)
    credit = Column(Float, default=0.0)
    description = Column(String(255))
    
    # Relationships
    journal_entry = relationship("JournalEntry", back_populates="lines")
    debit_account = relationship("Account", foreign_keys=[account_id], back_populates="debit_entries")
    credit_account = relationship("Account", foreign_keys=[account_id], back_populates="credit_entries")

class Invoice(Base, BaseModel):
    """Invoice model for sales and purchases."""
    __tablename__ = 'invoices'
    
    id = Column(Integer, primary_key=True, index=True)
    invoice_number = Column(String(20), unique=True, nullable=False)
    invoice_date = Column(Date, nullable=False)
    due_date = Column(Date)
    customer_id = Column(Integer, ForeignKey('customers.id'))
    vendor_id = Column(Integer, ForeignKey('vendors.id'))
    subtotal = Column(Float, default=0.0)
    tax_amount = Column(Float, default=0.0)
    total_amount = Column(Float, default=0.0)
    status = Column(String(20), default='draft')  # draft, sent, paid, cancelled
    notes = Column(String(500))
    
    # Relationships
    customer = relationship("Customer", back_populates="invoices")
    vendor = relationship("Vendor", back_populates="invoices")
    items = relationship("InvoiceLine", back_populates="invoice")
    created_by = Column(Integer, ForeignKey('users.id'))
    created_by_user = relationship("User", back_populates="created_invoices")

class InvoiceLine(Base):
    """Invoice Line model."""
    __tablename__ = 'invoice_lines'
    
    id = Column(Integer, primary_key=True, index=True)
    invoice_id = Column(Integer, ForeignKey('invoices.id'), nullable=False)
    product_id = Column(Integer, ForeignKey('products.id'))
    description = Column(String(255))
    quantity = Column(Float, default=1.0)
    unit_price = Column(Float, default=0.0)
    discount = Column(Float, default=0.0)
    tax_rate = Column(Float, default=0.0)
    total = Column(Float, default=0.0)
    
    # Relationships
    invoice = relationship("Invoice", back_populates="items")
    product = relationship("Product", back_populates="invoice_lines")
