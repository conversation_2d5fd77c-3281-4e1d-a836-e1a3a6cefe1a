import subprocess
import sys
import os
from pathlib import Path

def run_command(command, cwd=None):
    """Run a shell command."""
    try:
        result = subprocess.run(
            command,
            shell=True,
            check=True,
            cwd=cwd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        print(f"✅ {command}")
        if result.stdout:
            print(result.stdout)
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Error running '{command}':")
        print(f"Exit code: {e.returncode}")
        if e.stdout:
            print("=== STDOUT ===")
            print(e.stdout)
        if e.stderr:
            print("=== STDERR ===")
            print(e.stderr)
        return False

def main():
    """Main setup function."""
    print("🚀 Setting up Rabie <PERSON>")
    print("=" * 50)
    
    # Get the project root directory
    project_root = Path(__file__).parent.absolute()
    
    # 1. Create a virtual environment
    print("\n🔧 Setting up Python virtual environment...")
    venv_dir = project_root / "venv"
    if not venv_dir.exists():
        if not run_command(f"{sys.executable} -m venv {venv_dir}"):
            print("Failed to create virtual environment. Exiting.")
            return
    
    # Determine the correct pip and python commands based on the OS
    if os.name == 'nt':  # Windows
        pip_cmd = str(venv_dir / "Scripts" / "pip")
        python_cmd = str(venv_dir / "Scripts" / "python")
    else:  # Unix/Mac
        pip_cmd = str(venv_dir / "bin" / "pip")
        python_cmd = str(venv_dir / "bin" / "python")
    
    # 2. Install dependencies
    print("\n📦 Installing dependencies...")
    if not run_command(f"{pip_cmd} install --upgrade pip"):
        print("Failed to upgrade pip. Continuing anyway...")
    
    if not run_command(f"{pip_cmd} install -r requirements.txt"):
        print("Failed to install dependencies. Exiting.")
        return
    
    # 3. Initialize the database
    print("\n💾 Initializing database...")
    if not run_command(f"{python_cmd} -m database.initial_data"):
        print("Failed to initialize database. Exiting.")
        return
    
    print("\n✨ Setup completed successfully! ✨")
    print("=" * 50)
    print("\nTo activate the virtual environment, run:")
    if os.name == 'nt':  # Windows
        print(f"  {venv_dir}\\Scripts\\activate")
    else:  # Unix/Mac
        print(f"  source {venv_dir}/bin/activate")
    
    print("\nTo run the application, use:")
    print(f"  {python_cmd} main.py")

if __name__ == "__main__":
    main()
