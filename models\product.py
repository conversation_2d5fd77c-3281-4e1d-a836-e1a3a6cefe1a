from sqlalchemy import Column, String, Integer, Numeric, Text, Boolean, ForeignKey
from sqlalchemy.orm import relationship
from .base import BaseModel

class ProductCategory(BaseModel):
    """Product category model for organizing products."""
    __tablename__ = "product_categories"
    
    name = Column(String(50), unique=True, nullable=False)
    description = Column(Text, nullable=True)
    
    # Relationships
    products = relationship("Product", back_populates="category")
    
    def __repr__(self):
        return f"<Category {self.name}>"

class Product(BaseModel):
    """Product model for inventory and sales."""
    __tablename__ = "products"
    
    # Basic information
    code = Column(String(20), unique=True, index=True, nullable=False)
    name = Column(String(100), nullable=False, index=True)
    description = Column(Text, nullable=True)
    
    # Pricing
    purchase_price = Column(Numeric(15, 2), default=0.0)
    selling_price = Column(Numeric(15, 2), default=0.0)
    tax_rate = Column(Numeric(5, 2), default=0.0)  # Tax rate as percentage
    
    # Inventory
    sku = Column(String(50), unique=True, nullable=True)
    barcode = Column(String(50), unique=True, nullable=True)
    quantity_in_stock = Column(Integer, default=0)
    minimum_stock_level = Column(Integer, default=10)
    
    # Category relationship
    category_id = Column(Integer, ForeignKey('product_categories.id'), nullable=True)
    category = relationship("ProductCategory", back_populates="products")
    
    # Status
    is_active = Column(Boolean, default=True)
    
    # Relationships
    invoice_lines = relationship("InvoiceLine", back_populates="product")
    
    def __repr__(self):
        return f"<Product {self.code} - {self.name}>"
    
    @property
    def needs_restock(self):
        """Check if product needs to be restocked."""
        return self.quantity_in_stock <= self.minimum_stock_level
    
    def update_stock(self, quantity):
        """Update stock quantity."""
        self.quantity_in_stock += quantity
