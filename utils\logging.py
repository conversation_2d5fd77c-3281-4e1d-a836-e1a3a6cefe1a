"""
Logging configuration and utilities.

This module provides a centralized logging configuration and utility functions
for consistent logging throughout the application.
"""
import logging
import logging.handlers
import os
import sys
from pathlib import Path
from typing import Optional, Dict, Any, Union

from config.settings import PATHS, AppSettings

# Logging format
LOG_FORMAT = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
DATE_FORMAT = "%Y-%m-%d %H:%M:%S"

# Log file paths
LOG_DIR = PATHS.data / 'logs'
LOG_FILE = LOG_DIR / 'app.log'
ERROR_LOG_FILE = LOG_DIR / 'error.log'

# Ensure log directory exists
LOG_DIR.mkdir(parents=True, exist_ok=True)

# Logging levels
LOG_LEVELS = {
    'debug': logging.DEBUG,
    'info': logging.INFO,
    'warning': logging.WARNING,
    'error': logging.ERROR,
    'critical': logging.CRITICAL,
}

class ColoredFormatter(logging.Formatter):
    """Custom formatter for adding colors to log messages."""
    
    # ANSI color codes
    COLORS = {
        'DEBUG': '\033[36m',      # Cyan
        'INFO': '\033[32m',       # Green
        'WARNING': '\033[33m',    # Yellow
        'ERROR': '\033[31m',      # Red
        'CRITICAL': '\033[31;1m', # Bright Red
        'RESET': '\033[0m'        # Reset to default
    }
    
    def format(self, record):
        """Format the specified record as text with colors."""
        # Get the default formatted message
        message = super().format(record)
        
        # Add color if the output is a terminal
        if sys.stderr.isatty():
            levelname = record.levelname
            if levelname in self.COLORS:
                message = f"{self.COLORS[levelname]}{message}{self.COLORS['RESET']}"
        
        return message

def setup_logging(
    level: Union[str, int] = logging.INFO,
    log_file: Optional[Union[str, Path]] = None,
    error_log_file: Optional[Union[str, Path]] = None,
    max_bytes: int = 10 * 1024 * 1024,  # 10 MB
    backup_count: int = 5,
    console: bool = True
) -> None:
    """
    Set up logging configuration.
    
    Args:
        level: Logging level as string or int
        log_file: Path to the main log file
        error_log_file: Path to the error log file
        max_bytes: Maximum log file size before rotation
        backup_count: Number of backup log files to keep
        console: Whether to log to console
    """
    # Convert string log level to int if needed
    if isinstance(level, str):
        level = LOG_LEVELS.get(level.lower(), logging.INFO)
    
    # Set up the root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(level)
    
    # Clear any existing handlers
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)
    
    # Create formatters
    file_formatter = logging.Formatter(LOG_FORMAT, DATE_FORMAT)
    console_formatter = ColoredFormatter(
        "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        DATE_FORMAT
    )
    
    # Set up file handler for all logs
    if log_file:
        file_handler = logging.handlers.RotatingFileHandler(
            log_file,
            maxBytes=max_bytes,
            backupCount=backup_count,
            encoding='utf-8'
        )
        file_handler.setFormatter(file_formatter)
        file_handler.setLevel(level)
        root_logger.addHandler(file_handler)
    
    # Set up error log file handler
    if error_log_file:
        error_handler = logging.handlers.RotatingFileHandler(
            error_log_file,
            maxBytes=max_bytes,
            backupCount=backup_count,
            encoding='utf-8'
        )
        error_handler.setFormatter(file_formatter)
        error_handler.setLevel(logging.ERROR)
        root_logger.addHandler(error_handler)
    
    # Set up console handler
    if console:
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(console_formatter)
        console_handler.setLevel(level)
        root_logger.addHandler(console_handler)
    
    # Configure SQLAlchemy logging
    sqlalchemy_logger = logging.getLogger('sqlalchemy')
    sqlalchemy_logger.setLevel(logging.WARNING)
    
    # Configure other third-party loggers
    for logger_name in ['urllib3', 'requests', 'matplotlib', 'PIL']:
        logging.getLogger(logger_name).setLevel(logging.WARNING)

def get_logger(name: str = None) -> logging.Logger:
    """
    Get a logger with the specified name.
    
    Args:
        name: Logger name (usually __name__)
        
    Returns:
        Configured logger instance
    """
    logger = logging.getLogger(name or __name__)
    return logger

# Set up default logging configuration
setup_logging(
    level=logging.DEBUG if AppSettings.DEBUG else logging.INFO,
    log_file=LOG_FILE,
    error_log_file=ERROR_LOG_FILE,
    console=True
)

# Create a default logger for this module
logger = get_logger(__name__)

def log_exceptions(logger: logging.Logger = None):
    """
    Decorator to log exceptions raised by the decorated function.
    
    Args:
        logger: Logger instance to use (defaults to module logger)
    """
    if logger is None:
        logger = globals().get('logger', get_logger(__name__))
    
    def decorator(func):
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                logger.exception(
                    f"Exception in {func.__module__}.{func.__name__}: {str(e)}"
                )
                raise
        return wrapper
    return decorator

def log_execution(logger: logging.Logger = None, level: int = logging.DEBUG):
    """
    Decorator to log function entry and exit.
    
    Args:
        logger: Logger instance to use (defaults to module logger)
        level: Logging level to use for the messages
    """
    if logger is None:
        logger = globals().get('logger', get_logger(__name__))
    
    def decorator(func):
        def wrapper(*args, **kwargs):
            logger.log(level, f"Entering {func.__module__}.{func.__name__}")
            try:
                result = func(*args, **kwargs)
                logger.log(level, f"Exiting {func.__module__}.{func.__name__}")
                return result
            except Exception as e:
                logger.exception(
                    f"Exception in {func.__module__}.{func.__name__}: {str(e)}"
                )
                raise
        return wrapper
    return decorator
