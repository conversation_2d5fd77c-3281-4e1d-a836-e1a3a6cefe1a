import os
import pyodbc
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

try:
    # Build connection string from environment variables
    connection_string = (
        f"DRIVER={os.getenv('DB_DRIVER_NAME')};"
        f"SERVER={os.getenv('DB_HOST')},{os.getenv('DB_PORT')};"
        f"DATABASE={os.getenv('DB_NAME')};"
        f"UID={os.getenv('DB_USER')};"
        f"PWD={os.getenv('DB_PASSWORD')};"
        "Trusted_Connection=no;"
        "Encrypt=no;"
    )
    
    print("Attempting to connect to SQL Server...")
    print(f"Connection string: {connection_string.replace(os.getenv('DB_PASSWORD'), '*****')}")
    
    # Try to establish a connection
    conn = pyodbc.connect(connection_string)
    cursor = conn.cursor()
    
    # Test the connection
    cursor.execute("SELECT @@version;")
    row = cursor.fetchone()
    print("\nConnection successful!")
    print(f"SQL Server version: {row[0]}")
    
    # Close the connection
    conn.close()
    
except pyodbc.Error as e:
    print("\nError connecting to SQL Server:")
    print(f"SQL Error: {e}")
    print("\nPlease check the following:")
    print(f"1. Is SQL Server running on {os.getenv('DB_HOST')}:{os.getenv('DB_PORT')}?")
    print(f"2. Is the database '{os.getenv('DB_NAME')}' created?")
    print(f"3. Does the user '{os.getenv('DB_USER')}' have the correct permissions?")
    print("4. Is the SQL Server configured to accept SQL Server Authentication?")
    print("5. Is the SQL Server Browser service running?")
    print("\nFor development, you can switch to SQLite by updating the .env file:")
    print("DB_DRIVER=sqlite")
    print("DB_NAME=rabie_alkhaleej.db")
