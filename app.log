2025-07-04 21:59:22,741 - __main__ - INFO - Starting application initialization
2025-07-04 21:59:22,780 - config.settings - INFO - Application starting in development mode
2025-07-04 21:59:22,780 - __main__ - CRITICAL - Fatal error: 'host'
Traceback (most recent call last):
  File "C:\HamzaZareiProgram\RabieAlKhaleejApp\main.py", line 49, in main
    from ui.main_window import MainWindow
  File "C:\HamzaZareiProgram\RabieAlKhaleejApp\ui\main_window.py", line 9, in <module>
    from config import settings
  File "C:\HamzaZareiProgram\RabieAlKhaleejApp\config\settings.py", line 160, in <module>
    logger.info(f"Database: {DB_CONFIG['drivername']}://{'*' * 8}@{DB_CONFIG['host']}/{DB_CONFIG['database']}")
                                                                   ~~~~~~~~~^^^^^^^^
KeyError: 'host'
2025-07-04 22:00:04,913 - __main__ - INFO - Starting application initialization
2025-07-04 22:00:04,955 - config.settings - INFO - Application starting in development mode
2025-07-04 22:00:04,955 - config.settings - INFO - Database: sqlite:///./data/rabie_alkhaleej.db
2025-07-04 22:00:05,218 - __main__ - INFO - Initializing database...
2025-07-04 22:00:05,234 - __main__ - ERROR - Failed to initialize database: name 'Boolean' is not defined
Traceback (most recent call last):
  File "C:\HamzaZareiProgram\RabieAlKhaleejApp\main.py", line 55, in main
    init_db()
  File "C:\HamzaZareiProgram\RabieAlKhaleejApp\database\session.py", line 64, in init_db
    from models.customer import Customer
  File "C:\HamzaZareiProgram\RabieAlKhaleejApp\models\customer.py", line 6, in <module>
    class Customer(BaseModel):
  File "C:\HamzaZareiProgram\RabieAlKhaleejApp\models\customer.py", line 34, in Customer
    is_active = Column(Boolean, default=True)
                       ^^^^^^^
NameError: name 'Boolean' is not defined
2025-07-04 22:00:05,235 - __main__ - CRITICAL - Fatal error: name 'Boolean' is not defined
Traceback (most recent call last):
  File "C:\HamzaZareiProgram\RabieAlKhaleejApp\main.py", line 55, in main
    init_db()
  File "C:\HamzaZareiProgram\RabieAlKhaleejApp\database\session.py", line 64, in init_db
    from models.customer import Customer
  File "C:\HamzaZareiProgram\RabieAlKhaleejApp\models\customer.py", line 6, in <module>
    class Customer(BaseModel):
  File "C:\HamzaZareiProgram\RabieAlKhaleejApp\models\customer.py", line 34, in Customer
    is_active = Column(Boolean, default=True)
                       ^^^^^^^
NameError: name 'Boolean' is not defined
2025-07-04 22:01:11,310 - __main__ - INFO - Starting application initialization
2025-07-04 22:01:11,349 - config.settings - INFO - Application starting in development mode
2025-07-04 22:01:11,349 - config.settings - INFO - Database: sqlite:///./data/rabie_alkhaleej.db
2025-07-04 22:01:11,584 - __main__ - INFO - Initializing database...
2025-07-04 22:01:11,609 - __main__ - ERROR - Failed to initialize database: name 'Text' is not defined
Traceback (most recent call last):
  File "C:\HamzaZareiProgram\RabieAlKhaleejApp\main.py", line 55, in main
    init_db()
  File "C:\HamzaZareiProgram\RabieAlKhaleejApp\database\session.py", line 66, in init_db
    from models.invoice import Invoice, InvoiceLine
  File "C:\HamzaZareiProgram\RabieAlKhaleejApp\models\invoice.py", line 21, in <module>
    class Invoice(BaseModel):
  File "C:\HamzaZareiProgram\RabieAlKhaleejApp\models\invoice.py", line 46, in Invoice
    notes = Column(Text, nullable=True)
                   ^^^^
NameError: name 'Text' is not defined
2025-07-04 22:01:11,610 - __main__ - CRITICAL - Fatal error: name 'Text' is not defined
Traceback (most recent call last):
  File "C:\HamzaZareiProgram\RabieAlKhaleejApp\main.py", line 55, in main
    init_db()
  File "C:\HamzaZareiProgram\RabieAlKhaleejApp\database\session.py", line 66, in init_db
    from models.invoice import Invoice, InvoiceLine
  File "C:\HamzaZareiProgram\RabieAlKhaleejApp\models\invoice.py", line 21, in <module>
    class Invoice(BaseModel):
  File "C:\HamzaZareiProgram\RabieAlKhaleejApp\models\invoice.py", line 46, in Invoice
    notes = Column(Text, nullable=True)
                   ^^^^
NameError: name 'Text' is not defined
2025-07-04 22:07:08,837 - __main__ - INFO - Starting application initialization
2025-07-04 22:07:08,874 - config.settings - INFO - Application starting in development mode
2025-07-04 22:07:08,874 - config.settings - INFO - Database: sqlite:///./data/rabie_alkhaleej.db
2025-07-04 22:07:09,097 - __main__ - INFO - Initializing database...
2025-07-04 22:07:09,114 - __main__ - ERROR - Failed to initialize database: name 'Text' is not defined
Traceback (most recent call last):
  File "C:\HamzaZareiProgram\RabieAlKhaleejApp\main.py", line 55, in main
    init_db()
  File "C:\HamzaZareiProgram\RabieAlKhaleejApp\database\session.py", line 66, in init_db
    from models.invoice import Invoice, InvoiceLine
  File "C:\HamzaZareiProgram\RabieAlKhaleejApp\models\invoice.py", line 21, in <module>
    class Invoice(BaseModel):
  File "C:\HamzaZareiProgram\RabieAlKhaleejApp\models\invoice.py", line 46, in Invoice
    notes = Column(Text, nullable=True)
                   ^^^^
NameError: name 'Text' is not defined
2025-07-04 22:07:09,115 - __main__ - CRITICAL - Fatal error: name 'Text' is not defined
Traceback (most recent call last):
  File "C:\HamzaZareiProgram\RabieAlKhaleejApp\main.py", line 55, in main
    init_db()
  File "C:\HamzaZareiProgram\RabieAlKhaleejApp\database\session.py", line 66, in init_db
    from models.invoice import Invoice, InvoiceLine
  File "C:\HamzaZareiProgram\RabieAlKhaleejApp\models\invoice.py", line 21, in <module>
    class Invoice(BaseModel):
  File "C:\HamzaZareiProgram\RabieAlKhaleejApp\models\invoice.py", line 46, in Invoice
    notes = Column(Text, nullable=True)
                   ^^^^
NameError: name 'Text' is not defined
2025-07-04 22:07:48,386 - __main__ - INFO - Starting application initialization
2025-07-04 22:07:48,426 - config.settings - INFO - Application starting in development mode
2025-07-04 22:07:48,427 - config.settings - INFO - Database: sqlite:///./data/rabie_alkhaleej.db
2025-07-04 22:07:48,650 - __main__ - INFO - Initializing database...
2025-07-04 22:07:48,673 - __main__ - ERROR - Failed to initialize database: name 'Base' is not defined
Traceback (most recent call last):
  File "C:\HamzaZareiProgram\RabieAlKhaleejApp\main.py", line 55, in main
    init_db()
  File "C:\HamzaZareiProgram\RabieAlKhaleejApp\database\session.py", line 69, in init_db
    Base.metadata.create_all(bind=engine)
    ^^^^
NameError: name 'Base' is not defined
2025-07-04 22:07:48,673 - __main__ - CRITICAL - Fatal error: name 'Base' is not defined
Traceback (most recent call last):
  File "C:\HamzaZareiProgram\RabieAlKhaleejApp\main.py", line 55, in main
    init_db()
  File "C:\HamzaZareiProgram\RabieAlKhaleejApp\database\session.py", line 69, in init_db
    Base.metadata.create_all(bind=engine)
    ^^^^
NameError: name 'Base' is not defined
2025-07-04 22:08:09,156 - __main__ - INFO - Starting application initialization
2025-07-04 22:08:09,192 - config.settings - INFO - Application starting in development mode
2025-07-04 22:08:09,192 - config.settings - INFO - Database: sqlite:///./data/rabie_alkhaleej.db
2025-07-04 22:08:09,410 - __main__ - INFO - Initializing database...
2025-07-04 22:08:09,432 - __main__ - ERROR - Failed to initialize database: name 'Base' is not defined
Traceback (most recent call last):
  File "C:\HamzaZareiProgram\RabieAlKhaleejApp\main.py", line 55, in main
    init_db()
  File "C:\HamzaZareiProgram\RabieAlKhaleejApp\database\session.py", line 69, in init_db
    Base.metadata.create_all(bind=engine)
    ^^^^
NameError: name 'Base' is not defined
2025-07-04 22:08:09,432 - __main__ - CRITICAL - Fatal error: name 'Base' is not defined
Traceback (most recent call last):
  File "C:\HamzaZareiProgram\RabieAlKhaleejApp\main.py", line 55, in main
    init_db()
  File "C:\HamzaZareiProgram\RabieAlKhaleejApp\database\session.py", line 69, in init_db
    Base.metadata.create_all(bind=engine)
    ^^^^
NameError: name 'Base' is not defined
2025-07-04 22:17:42,716 - __main__ - INFO - Starting application initialization
2025-07-04 22:17:42,756 - config.settings - INFO - Application starting in development mode
2025-07-04 22:17:42,756 - config.settings - INFO - Database: sqlite:///./data/rabie_alkhaleej.db
2025-07-04 22:17:42,993 - __main__ - INFO - Initializing database...
2025-07-04 22:17:43,020 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-04 22:17:43,020 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("users")
2025-07-04 22:17:43,021 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-04 22:17:43,023 - sqlalchemy.engine.Engine - INFO - PRAGMA temp.table_info("users")
2025-07-04 22:17:43,023 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-04 22:17:43,023 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("customers")
2025-07-04 22:17:43,023 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-04 22:17:43,023 - sqlalchemy.engine.Engine - INFO - PRAGMA temp.table_info("customers")
2025-07-04 22:17:43,023 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-04 22:17:43,023 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("product_categories")
2025-07-04 22:17:43,023 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-04 22:17:43,024 - sqlalchemy.engine.Engine - INFO - PRAGMA temp.table_info("product_categories")
2025-07-04 22:17:43,024 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-04 22:17:43,024 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("products")
2025-07-04 22:17:43,024 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-04 22:17:43,024 - sqlalchemy.engine.Engine - INFO - PRAGMA temp.table_info("products")
2025-07-04 22:17:43,024 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-04 22:17:43,024 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("invoices")
2025-07-04 22:17:43,024 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-04 22:17:43,025 - sqlalchemy.engine.Engine - INFO - PRAGMA temp.table_info("invoices")
2025-07-04 22:17:43,025 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-04 22:17:43,025 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("invoice_lines")
2025-07-04 22:17:43,025 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-04 22:17:43,025 - sqlalchemy.engine.Engine - INFO - PRAGMA temp.table_info("invoice_lines")
2025-07-04 22:17:43,025 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-04 22:17:43,026 - sqlalchemy.engine.Engine - INFO - 
CREATE TABLE users (
	username VARCHAR(50) NOT NULL, 
	email VARCHAR(100), 
	hashed_password VARCHAR(100) NOT NULL, 
	full_name VARCHAR(100), 
	is_active BOOLEAN, 
	is_admin BOOLEAN, 
	last_login DATETIME, 
	phone VARCHAR(20), 
	address VARCHAR(200), 
	id INTEGER NOT NULL, 
	created_at DATETIME NOT NULL, 
	updated_at DATETIME NOT NULL, 
	PRIMARY KEY (id)
)


2025-07-04 22:17:43,026 - sqlalchemy.engine.Engine - INFO - [no key 0.00011s] ()
2025-07-04 22:17:43,033 - sqlalchemy.engine.Engine - INFO - CREATE UNIQUE INDEX ix_users_email ON users (email)
2025-07-04 22:17:43,033 - sqlalchemy.engine.Engine - INFO - [no key 0.00012s] ()
2025-07-04 22:17:43,037 - sqlalchemy.engine.Engine - INFO - CREATE UNIQUE INDEX ix_users_username ON users (username)
2025-07-04 22:17:43,037 - sqlalchemy.engine.Engine - INFO - [no key 0.00011s] ()
2025-07-04 22:17:43,040 - sqlalchemy.engine.Engine - INFO - CREATE INDEX ix_users_id ON users (id)
2025-07-04 22:17:43,040 - sqlalchemy.engine.Engine - INFO - [no key 0.00010s] ()
2025-07-04 22:17:43,045 - sqlalchemy.engine.Engine - INFO - 
CREATE TABLE customers (
	name VARCHAR(100) NOT NULL, 
	code VARCHAR(20) NOT NULL, 
	contact_person VARCHAR(100), 
	phone VARCHAR(20), 
	mobile VARCHAR(20), 
	email VARCHAR(100), 
	address TEXT, 
	city VARCHAR(50), 
	state VARCHAR(50), 
	postal_code VARCHAR(20), 
	country VARCHAR(50), 
	tax_id VARCHAR(50), 
	credit_limit NUMERIC(15, 2), 
	current_balance NUMERIC(15, 2), 
	notes TEXT, 
	is_active BOOLEAN, 
	id INTEGER NOT NULL, 
	created_at DATETIME NOT NULL, 
	updated_at DATETIME NOT NULL, 
	PRIMARY KEY (id)
)


2025-07-04 22:17:43,045 - sqlalchemy.engine.Engine - INFO - [no key 0.00017s] ()
2025-07-04 22:17:43,049 - sqlalchemy.engine.Engine - INFO - CREATE UNIQUE INDEX ix_customers_code ON customers (code)
2025-07-04 22:17:43,049 - sqlalchemy.engine.Engine - INFO - [no key 0.00011s] ()
2025-07-04 22:17:43,053 - sqlalchemy.engine.Engine - INFO - CREATE INDEX ix_customers_name ON customers (name)
2025-07-04 22:17:43,053 - sqlalchemy.engine.Engine - INFO - [no key 0.00011s] ()
2025-07-04 22:17:43,057 - sqlalchemy.engine.Engine - INFO - CREATE INDEX ix_customers_id ON customers (id)
2025-07-04 22:17:43,057 - sqlalchemy.engine.Engine - INFO - [no key 0.00012s] ()
2025-07-04 22:17:43,060 - sqlalchemy.engine.Engine - INFO - 
CREATE TABLE product_categories (
	name VARCHAR(50) NOT NULL, 
	description TEXT, 
	id INTEGER NOT NULL, 
	created_at DATETIME NOT NULL, 
	updated_at DATETIME NOT NULL, 
	PRIMARY KEY (id), 
	UNIQUE (name)
)


2025-07-04 22:17:43,060 - sqlalchemy.engine.Engine - INFO - [no key 0.00012s] ()
2025-07-04 22:17:43,065 - sqlalchemy.engine.Engine - INFO - CREATE INDEX ix_product_categories_id ON product_categories (id)
2025-07-04 22:17:43,066 - sqlalchemy.engine.Engine - INFO - [no key 0.00011s] ()
2025-07-04 22:17:43,074 - sqlalchemy.engine.Engine - INFO - 
CREATE TABLE products (
	code VARCHAR(20) NOT NULL, 
	name VARCHAR(100) NOT NULL, 
	description TEXT, 
	purchase_price NUMERIC(15, 2), 
	selling_price NUMERIC(15, 2), 
	tax_rate NUMERIC(5, 2), 
	sku VARCHAR(50), 
	barcode VARCHAR(50), 
	quantity_in_stock INTEGER, 
	minimum_stock_level INTEGER, 
	category_id INTEGER, 
	is_active BOOLEAN, 
	id INTEGER NOT NULL, 
	created_at DATETIME NOT NULL, 
	updated_at DATETIME NOT NULL, 
	PRIMARY KEY (id), 
	UNIQUE (sku), 
	UNIQUE (barcode), 
	FOREIGN KEY(category_id) REFERENCES product_categories (id)
)


2025-07-04 22:17:43,074 - sqlalchemy.engine.Engine - INFO - [no key 0.00013s] ()
2025-07-04 22:17:43,081 - sqlalchemy.engine.Engine - INFO - CREATE INDEX ix_products_name ON products (name)
2025-07-04 22:17:43,081 - sqlalchemy.engine.Engine - INFO - [no key 0.00015s] ()
2025-07-04 22:17:43,088 - sqlalchemy.engine.Engine - INFO - CREATE UNIQUE INDEX ix_products_code ON products (code)
2025-07-04 22:17:43,088 - sqlalchemy.engine.Engine - INFO - [no key 0.00010s] ()
2025-07-04 22:17:43,094 - sqlalchemy.engine.Engine - INFO - CREATE INDEX ix_products_id ON products (id)
2025-07-04 22:17:43,094 - sqlalchemy.engine.Engine - INFO - [no key 0.00021s] ()
2025-07-04 22:17:43,098 - sqlalchemy.engine.Engine - INFO - 
CREATE TABLE invoices (
	invoice_number VARCHAR(20) NOT NULL, 
	invoice_date DATETIME NOT NULL, 
	due_date DATETIME NOT NULL, 
	status VARCHAR(9), 
	is_paid BOOLEAN, 
	subtotal NUMERIC(15, 2), 
	tax_amount NUMERIC(15, 2), 
	discount_amount NUMERIC(15, 2), 
	total_amount NUMERIC(15, 2), 
	payment_method VARCHAR(13), 
	payment_date DATETIME, 
	payment_reference VARCHAR(100), 
	notes TEXT, 
	terms_and_conditions TEXT, 
	customer_id INTEGER NOT NULL, 
	user_id INTEGER NOT NULL, 
	id INTEGER NOT NULL, 
	created_at DATETIME NOT NULL, 
	updated_at DATETIME NOT NULL, 
	PRIMARY KEY (id), 
	FOREIGN KEY(customer_id) REFERENCES customers (id), 
	FOREIGN KEY(user_id) REFERENCES users (id)
)


2025-07-04 22:17:43,098 - sqlalchemy.engine.Engine - INFO - [no key 0.00013s] ()
2025-07-04 22:17:43,101 - sqlalchemy.engine.Engine - INFO - CREATE UNIQUE INDEX ix_invoices_invoice_number ON invoices (invoice_number)
2025-07-04 22:17:43,101 - sqlalchemy.engine.Engine - INFO - [no key 0.00011s] ()
2025-07-04 22:17:43,106 - sqlalchemy.engine.Engine - INFO - CREATE INDEX ix_invoices_id ON invoices (id)
2025-07-04 22:17:43,106 - sqlalchemy.engine.Engine - INFO - [no key 0.00010s] ()
2025-07-04 22:17:43,110 - sqlalchemy.engine.Engine - INFO - 
CREATE TABLE invoice_lines (
	invoice_id INTEGER NOT NULL, 
	product_id INTEGER NOT NULL, 
	description VARCHAR(200) NOT NULL, 
	quantity NUMERIC(15, 3), 
	unit_price NUMERIC(15, 2) NOT NULL, 
	tax_rate NUMERIC(5, 2), 
	discount_percent NUMERIC(5, 2), 
	id INTEGER NOT NULL, 
	created_at DATETIME NOT NULL, 
	updated_at DATETIME NOT NULL, 
	PRIMARY KEY (id), 
	FOREIGN KEY(invoice_id) REFERENCES invoices (id), 
	FOREIGN KEY(product_id) REFERENCES products (id)
)


2025-07-04 22:17:43,110 - sqlalchemy.engine.Engine - INFO - [no key 0.00019s] ()
2025-07-04 22:17:43,114 - sqlalchemy.engine.Engine - INFO - CREATE INDEX ix_invoice_lines_id ON invoice_lines (id)
2025-07-04 22:17:43,114 - sqlalchemy.engine.Engine - INFO - [no key 0.00011s] ()
2025-07-04 22:17:43,118 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-04 22:17:43,118 - __main__ - INFO - Database initialized successfully
2025-07-04 22:17:43,118 - __main__ - INFO - Creating application instance
2025-07-04 22:17:43,134 - __main__ - INFO - Creating main window
2025-07-05 20:21:45,361 - __main__ - INFO - Starting application initialization
2025-07-05 20:21:45,403 - config.settings - INFO - Application starting in development mode
2025-07-05 20:21:45,403 - config.settings - INFO - Database: sqlite:///./data/rabie_alkhaleej.db
2025-07-05 20:21:45,652 - __main__ - INFO - Initializing database...
2025-07-05 20:21:45,678 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-05 20:21:45,678 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("users")
2025-07-05 20:21:45,678 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-05 20:21:45,681 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("customers")
2025-07-05 20:21:45,681 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-05 20:21:45,681 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("product_categories")
2025-07-05 20:21:45,681 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-05 20:21:45,682 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("products")
2025-07-05 20:21:45,682 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-05 20:21:45,682 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("invoices")
2025-07-05 20:21:45,682 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-05 20:21:45,682 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("invoice_lines")
2025-07-05 20:21:45,683 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-05 20:21:45,683 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-05 20:21:45,684 - __main__ - INFO - Database initialized successfully
2025-07-05 20:21:45,684 - __main__ - INFO - Creating application instance
2025-07-05 20:21:45,701 - __main__ - INFO - Creating main window
2025-07-05 20:22:35,635 - __main__ - INFO - Starting application initialization
2025-07-05 20:22:35,637 - __main__ - ERROR - Import error: No module named 'PyQt6'
Traceback (most recent call last):
  File "C:\HamzaZareiProgram\RabieAlKhaleejApp\main.py", line 48, in main
    from PyQt6.QtWidgets import QApplication, QMessageBox
ModuleNotFoundError: No module named 'PyQt6'
2025-07-06 12:06:05,892 - __main__ - INFO - Starting application initialization
2025-07-06 12:06:05,892 - __main__ - ERROR - Import error: No module named 'PyQt6'
Traceback (most recent call last):
  File "C:\HamzaZareiProgram\RabieAlKhaleejApp\main.py", line 48, in main
    from PyQt6.QtWidgets import QApplication, QMessageBox
ModuleNotFoundError: No module named 'PyQt6'
2025-07-06 12:07:07,298 - __main__ - INFO - Starting application initialization
2025-07-06 12:07:07,411 - config.settings - INFO - Application starting in development mode
2025-07-06 12:07:07,411 - config.settings - INFO - Database: sqlite:///./data/rabie_alkhaleej.db
2025-07-06 12:07:07,738 - __main__ - INFO - Initializing database...
2025-07-06 12:07:07,764 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-06 12:07:07,764 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("users")
2025-07-06 12:07:07,764 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 12:07:07,768 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("customers")
2025-07-06 12:07:07,768 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 12:07:07,768 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("product_categories")
2025-07-06 12:07:07,769 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 12:07:07,769 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("products")
2025-07-06 12:07:07,769 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 12:07:07,770 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("invoices")
2025-07-06 12:07:07,770 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 12:07:07,770 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("invoice_lines")
2025-07-06 12:07:07,770 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 12:07:07,771 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-06 12:07:07,771 - __main__ - INFO - Database initialized successfully
2025-07-06 12:07:07,771 - __main__ - INFO - Creating application instance
2025-07-06 12:07:07,815 - __main__ - INFO - Creating main window
2025-07-06 12:21:41,607 - __main__ - INFO - Starting application initialization
2025-07-06 12:21:41,649 - config.settings - INFO - Application starting in development mode
2025-07-06 12:21:41,650 - config.settings - INFO - Database: sqlite:///./data/rabie_alkhaleej.db
2025-07-06 12:21:41,921 - __main__ - INFO - Initializing database...
2025-07-06 12:21:41,947 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-06 12:21:41,947 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("users")
2025-07-06 12:21:41,948 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 12:21:41,948 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("customers")
2025-07-06 12:21:41,949 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 12:21:41,949 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("product_categories")
2025-07-06 12:21:41,949 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 12:21:41,949 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("products")
2025-07-06 12:21:41,950 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 12:21:41,950 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("invoices")
2025-07-06 12:21:41,950 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 12:21:41,950 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("invoice_lines")
2025-07-06 12:21:41,950 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 12:21:41,951 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-06 12:21:41,951 - __main__ - INFO - Database initialized successfully
2025-07-06 12:21:41,951 - __main__ - INFO - Creating application instance
2025-07-06 12:21:41,971 - __main__ - INFO - Creating main window
