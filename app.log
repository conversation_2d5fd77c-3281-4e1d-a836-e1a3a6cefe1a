2025-07-04 21:59:22,741 - __main__ - INFO - Starting application initialization
2025-07-04 21:59:22,780 - config.settings - INFO - Application starting in development mode
2025-07-04 21:59:22,780 - __main__ - CRITICAL - Fatal error: 'host'
Traceback (most recent call last):
  File "C:\HamzaZareiProgram\RabieAlKhaleejApp\main.py", line 49, in main
    from ui.main_window import MainWindow
  File "C:\HamzaZareiProgram\RabieAlKhaleejApp\ui\main_window.py", line 9, in <module>
    from config import settings
  File "C:\HamzaZareiProgram\RabieAlKhaleejApp\config\settings.py", line 160, in <module>
    logger.info(f"Database: {DB_CONFIG['drivername']}://{'*' * 8}@{DB_CONFIG['host']}/{DB_CONFIG['database']}")
                                                                   ~~~~~~~~~^^^^^^^^
KeyError: 'host'
2025-07-04 22:00:04,913 - __main__ - INFO - Starting application initialization
2025-07-04 22:00:04,955 - config.settings - INFO - Application starting in development mode
2025-07-04 22:00:04,955 - config.settings - INFO - Database: sqlite:///./data/rabie_alkhaleej.db
2025-07-04 22:00:05,218 - __main__ - INFO - Initializing database...
2025-07-04 22:00:05,234 - __main__ - ERROR - Failed to initialize database: name 'Boolean' is not defined
Traceback (most recent call last):
  File "C:\HamzaZareiProgram\RabieAlKhaleejApp\main.py", line 55, in main
    init_db()
  File "C:\HamzaZareiProgram\RabieAlKhaleejApp\database\session.py", line 64, in init_db
    from models.customer import Customer
  File "C:\HamzaZareiProgram\RabieAlKhaleejApp\models\customer.py", line 6, in <module>
    class Customer(BaseModel):
  File "C:\HamzaZareiProgram\RabieAlKhaleejApp\models\customer.py", line 34, in Customer
    is_active = Column(Boolean, default=True)
                       ^^^^^^^
NameError: name 'Boolean' is not defined
2025-07-04 22:00:05,235 - __main__ - CRITICAL - Fatal error: name 'Boolean' is not defined
Traceback (most recent call last):
  File "C:\HamzaZareiProgram\RabieAlKhaleejApp\main.py", line 55, in main
    init_db()
  File "C:\HamzaZareiProgram\RabieAlKhaleejApp\database\session.py", line 64, in init_db
    from models.customer import Customer
  File "C:\HamzaZareiProgram\RabieAlKhaleejApp\models\customer.py", line 6, in <module>
    class Customer(BaseModel):
  File "C:\HamzaZareiProgram\RabieAlKhaleejApp\models\customer.py", line 34, in Customer
    is_active = Column(Boolean, default=True)
                       ^^^^^^^
NameError: name 'Boolean' is not defined
2025-07-04 22:01:11,310 - __main__ - INFO - Starting application initialization
2025-07-04 22:01:11,349 - config.settings - INFO - Application starting in development mode
2025-07-04 22:01:11,349 - config.settings - INFO - Database: sqlite:///./data/rabie_alkhaleej.db
2025-07-04 22:01:11,584 - __main__ - INFO - Initializing database...
2025-07-04 22:01:11,609 - __main__ - ERROR - Failed to initialize database: name 'Text' is not defined
Traceback (most recent call last):
  File "C:\HamzaZareiProgram\RabieAlKhaleejApp\main.py", line 55, in main
    init_db()
  File "C:\HamzaZareiProgram\RabieAlKhaleejApp\database\session.py", line 66, in init_db
    from models.invoice import Invoice, InvoiceLine
  File "C:\HamzaZareiProgram\RabieAlKhaleejApp\models\invoice.py", line 21, in <module>
    class Invoice(BaseModel):
  File "C:\HamzaZareiProgram\RabieAlKhaleejApp\models\invoice.py", line 46, in Invoice
    notes = Column(Text, nullable=True)
                   ^^^^
NameError: name 'Text' is not defined
2025-07-04 22:01:11,610 - __main__ - CRITICAL - Fatal error: name 'Text' is not defined
Traceback (most recent call last):
  File "C:\HamzaZareiProgram\RabieAlKhaleejApp\main.py", line 55, in main
    init_db()
  File "C:\HamzaZareiProgram\RabieAlKhaleejApp\database\session.py", line 66, in init_db
    from models.invoice import Invoice, InvoiceLine
  File "C:\HamzaZareiProgram\RabieAlKhaleejApp\models\invoice.py", line 21, in <module>
    class Invoice(BaseModel):
  File "C:\HamzaZareiProgram\RabieAlKhaleejApp\models\invoice.py", line 46, in Invoice
    notes = Column(Text, nullable=True)
                   ^^^^
NameError: name 'Text' is not defined
2025-07-04 22:07:08,837 - __main__ - INFO - Starting application initialization
2025-07-04 22:07:08,874 - config.settings - INFO - Application starting in development mode
2025-07-04 22:07:08,874 - config.settings - INFO - Database: sqlite:///./data/rabie_alkhaleej.db
2025-07-04 22:07:09,097 - __main__ - INFO - Initializing database...
2025-07-04 22:07:09,114 - __main__ - ERROR - Failed to initialize database: name 'Text' is not defined
Traceback (most recent call last):
  File "C:\HamzaZareiProgram\RabieAlKhaleejApp\main.py", line 55, in main
    init_db()
  File "C:\HamzaZareiProgram\RabieAlKhaleejApp\database\session.py", line 66, in init_db
    from models.invoice import Invoice, InvoiceLine
  File "C:\HamzaZareiProgram\RabieAlKhaleejApp\models\invoice.py", line 21, in <module>
    class Invoice(BaseModel):
  File "C:\HamzaZareiProgram\RabieAlKhaleejApp\models\invoice.py", line 46, in Invoice
    notes = Column(Text, nullable=True)
                   ^^^^
NameError: name 'Text' is not defined
2025-07-04 22:07:09,115 - __main__ - CRITICAL - Fatal error: name 'Text' is not defined
Traceback (most recent call last):
  File "C:\HamzaZareiProgram\RabieAlKhaleejApp\main.py", line 55, in main
    init_db()
  File "C:\HamzaZareiProgram\RabieAlKhaleejApp\database\session.py", line 66, in init_db
    from models.invoice import Invoice, InvoiceLine
  File "C:\HamzaZareiProgram\RabieAlKhaleejApp\models\invoice.py", line 21, in <module>
    class Invoice(BaseModel):
  File "C:\HamzaZareiProgram\RabieAlKhaleejApp\models\invoice.py", line 46, in Invoice
    notes = Column(Text, nullable=True)
                   ^^^^
NameError: name 'Text' is not defined
2025-07-04 22:07:48,386 - __main__ - INFO - Starting application initialization
2025-07-04 22:07:48,426 - config.settings - INFO - Application starting in development mode
2025-07-04 22:07:48,427 - config.settings - INFO - Database: sqlite:///./data/rabie_alkhaleej.db
2025-07-04 22:07:48,650 - __main__ - INFO - Initializing database...
2025-07-04 22:07:48,673 - __main__ - ERROR - Failed to initialize database: name 'Base' is not defined
Traceback (most recent call last):
  File "C:\HamzaZareiProgram\RabieAlKhaleejApp\main.py", line 55, in main
    init_db()
  File "C:\HamzaZareiProgram\RabieAlKhaleejApp\database\session.py", line 69, in init_db
    Base.metadata.create_all(bind=engine)
    ^^^^
NameError: name 'Base' is not defined
2025-07-04 22:07:48,673 - __main__ - CRITICAL - Fatal error: name 'Base' is not defined
Traceback (most recent call last):
  File "C:\HamzaZareiProgram\RabieAlKhaleejApp\main.py", line 55, in main
    init_db()
  File "C:\HamzaZareiProgram\RabieAlKhaleejApp\database\session.py", line 69, in init_db
    Base.metadata.create_all(bind=engine)
    ^^^^
NameError: name 'Base' is not defined
2025-07-04 22:08:09,156 - __main__ - INFO - Starting application initialization
2025-07-04 22:08:09,192 - config.settings - INFO - Application starting in development mode
2025-07-04 22:08:09,192 - config.settings - INFO - Database: sqlite:///./data/rabie_alkhaleej.db
2025-07-04 22:08:09,410 - __main__ - INFO - Initializing database...
2025-07-04 22:08:09,432 - __main__ - ERROR - Failed to initialize database: name 'Base' is not defined
Traceback (most recent call last):
  File "C:\HamzaZareiProgram\RabieAlKhaleejApp\main.py", line 55, in main
    init_db()
  File "C:\HamzaZareiProgram\RabieAlKhaleejApp\database\session.py", line 69, in init_db
    Base.metadata.create_all(bind=engine)
    ^^^^
NameError: name 'Base' is not defined
2025-07-04 22:08:09,432 - __main__ - CRITICAL - Fatal error: name 'Base' is not defined
Traceback (most recent call last):
  File "C:\HamzaZareiProgram\RabieAlKhaleejApp\main.py", line 55, in main
    init_db()
  File "C:\HamzaZareiProgram\RabieAlKhaleejApp\database\session.py", line 69, in init_db
    Base.metadata.create_all(bind=engine)
    ^^^^
NameError: name 'Base' is not defined
2025-07-04 22:17:42,716 - __main__ - INFO - Starting application initialization
2025-07-04 22:17:42,756 - config.settings - INFO - Application starting in development mode
2025-07-04 22:17:42,756 - config.settings - INFO - Database: sqlite:///./data/rabie_alkhaleej.db
2025-07-04 22:17:42,993 - __main__ - INFO - Initializing database...
2025-07-04 22:17:43,020 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-04 22:17:43,020 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("users")
2025-07-04 22:17:43,021 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-04 22:17:43,023 - sqlalchemy.engine.Engine - INFO - PRAGMA temp.table_info("users")
2025-07-04 22:17:43,023 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-04 22:17:43,023 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("customers")
2025-07-04 22:17:43,023 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-04 22:17:43,023 - sqlalchemy.engine.Engine - INFO - PRAGMA temp.table_info("customers")
2025-07-04 22:17:43,023 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-04 22:17:43,023 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("product_categories")
2025-07-04 22:17:43,023 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-04 22:17:43,024 - sqlalchemy.engine.Engine - INFO - PRAGMA temp.table_info("product_categories")
2025-07-04 22:17:43,024 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-04 22:17:43,024 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("products")
2025-07-04 22:17:43,024 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-04 22:17:43,024 - sqlalchemy.engine.Engine - INFO - PRAGMA temp.table_info("products")
2025-07-04 22:17:43,024 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-04 22:17:43,024 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("invoices")
2025-07-04 22:17:43,024 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-04 22:17:43,025 - sqlalchemy.engine.Engine - INFO - PRAGMA temp.table_info("invoices")
2025-07-04 22:17:43,025 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-04 22:17:43,025 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("invoice_lines")
2025-07-04 22:17:43,025 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-04 22:17:43,025 - sqlalchemy.engine.Engine - INFO - PRAGMA temp.table_info("invoice_lines")
2025-07-04 22:17:43,025 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-04 22:17:43,026 - sqlalchemy.engine.Engine - INFO - 
CREATE TABLE users (
	username VARCHAR(50) NOT NULL, 
	email VARCHAR(100), 
	hashed_password VARCHAR(100) NOT NULL, 
	full_name VARCHAR(100), 
	is_active BOOLEAN, 
	is_admin BOOLEAN, 
	last_login DATETIME, 
	phone VARCHAR(20), 
	address VARCHAR(200), 
	id INTEGER NOT NULL, 
	created_at DATETIME NOT NULL, 
	updated_at DATETIME NOT NULL, 
	PRIMARY KEY (id)
)


2025-07-04 22:17:43,026 - sqlalchemy.engine.Engine - INFO - [no key 0.00011s] ()
2025-07-04 22:17:43,033 - sqlalchemy.engine.Engine - INFO - CREATE UNIQUE INDEX ix_users_email ON users (email)
2025-07-04 22:17:43,033 - sqlalchemy.engine.Engine - INFO - [no key 0.00012s] ()
2025-07-04 22:17:43,037 - sqlalchemy.engine.Engine - INFO - CREATE UNIQUE INDEX ix_users_username ON users (username)
2025-07-04 22:17:43,037 - sqlalchemy.engine.Engine - INFO - [no key 0.00011s] ()
2025-07-04 22:17:43,040 - sqlalchemy.engine.Engine - INFO - CREATE INDEX ix_users_id ON users (id)
2025-07-04 22:17:43,040 - sqlalchemy.engine.Engine - INFO - [no key 0.00010s] ()
2025-07-04 22:17:43,045 - sqlalchemy.engine.Engine - INFO - 
CREATE TABLE customers (
	name VARCHAR(100) NOT NULL, 
	code VARCHAR(20) NOT NULL, 
	contact_person VARCHAR(100), 
	phone VARCHAR(20), 
	mobile VARCHAR(20), 
	email VARCHAR(100), 
	address TEXT, 
	city VARCHAR(50), 
	state VARCHAR(50), 
	postal_code VARCHAR(20), 
	country VARCHAR(50), 
	tax_id VARCHAR(50), 
	credit_limit NUMERIC(15, 2), 
	current_balance NUMERIC(15, 2), 
	notes TEXT, 
	is_active BOOLEAN, 
	id INTEGER NOT NULL, 
	created_at DATETIME NOT NULL, 
	updated_at DATETIME NOT NULL, 
	PRIMARY KEY (id)
)


2025-07-04 22:17:43,045 - sqlalchemy.engine.Engine - INFO - [no key 0.00017s] ()
2025-07-04 22:17:43,049 - sqlalchemy.engine.Engine - INFO - CREATE UNIQUE INDEX ix_customers_code ON customers (code)
2025-07-04 22:17:43,049 - sqlalchemy.engine.Engine - INFO - [no key 0.00011s] ()
2025-07-04 22:17:43,053 - sqlalchemy.engine.Engine - INFO - CREATE INDEX ix_customers_name ON customers (name)
2025-07-04 22:17:43,053 - sqlalchemy.engine.Engine - INFO - [no key 0.00011s] ()
2025-07-04 22:17:43,057 - sqlalchemy.engine.Engine - INFO - CREATE INDEX ix_customers_id ON customers (id)
2025-07-04 22:17:43,057 - sqlalchemy.engine.Engine - INFO - [no key 0.00012s] ()
2025-07-04 22:17:43,060 - sqlalchemy.engine.Engine - INFO - 
CREATE TABLE product_categories (
	name VARCHAR(50) NOT NULL, 
	description TEXT, 
	id INTEGER NOT NULL, 
	created_at DATETIME NOT NULL, 
	updated_at DATETIME NOT NULL, 
	PRIMARY KEY (id), 
	UNIQUE (name)
)


2025-07-04 22:17:43,060 - sqlalchemy.engine.Engine - INFO - [no key 0.00012s] ()
2025-07-04 22:17:43,065 - sqlalchemy.engine.Engine - INFO - CREATE INDEX ix_product_categories_id ON product_categories (id)
2025-07-04 22:17:43,066 - sqlalchemy.engine.Engine - INFO - [no key 0.00011s] ()
2025-07-04 22:17:43,074 - sqlalchemy.engine.Engine - INFO - 
CREATE TABLE products (
	code VARCHAR(20) NOT NULL, 
	name VARCHAR(100) NOT NULL, 
	description TEXT, 
	purchase_price NUMERIC(15, 2), 
	selling_price NUMERIC(15, 2), 
	tax_rate NUMERIC(5, 2), 
	sku VARCHAR(50), 
	barcode VARCHAR(50), 
	quantity_in_stock INTEGER, 
	minimum_stock_level INTEGER, 
	category_id INTEGER, 
	is_active BOOLEAN, 
	id INTEGER NOT NULL, 
	created_at DATETIME NOT NULL, 
	updated_at DATETIME NOT NULL, 
	PRIMARY KEY (id), 
	UNIQUE (sku), 
	UNIQUE (barcode), 
	FOREIGN KEY(category_id) REFERENCES product_categories (id)
)


2025-07-04 22:17:43,074 - sqlalchemy.engine.Engine - INFO - [no key 0.00013s] ()
2025-07-04 22:17:43,081 - sqlalchemy.engine.Engine - INFO - CREATE INDEX ix_products_name ON products (name)
2025-07-04 22:17:43,081 - sqlalchemy.engine.Engine - INFO - [no key 0.00015s] ()
2025-07-04 22:17:43,088 - sqlalchemy.engine.Engine - INFO - CREATE UNIQUE INDEX ix_products_code ON products (code)
2025-07-04 22:17:43,088 - sqlalchemy.engine.Engine - INFO - [no key 0.00010s] ()
2025-07-04 22:17:43,094 - sqlalchemy.engine.Engine - INFO - CREATE INDEX ix_products_id ON products (id)
2025-07-04 22:17:43,094 - sqlalchemy.engine.Engine - INFO - [no key 0.00021s] ()
2025-07-04 22:17:43,098 - sqlalchemy.engine.Engine - INFO - 
CREATE TABLE invoices (
	invoice_number VARCHAR(20) NOT NULL, 
	invoice_date DATETIME NOT NULL, 
	due_date DATETIME NOT NULL, 
	status VARCHAR(9), 
	is_paid BOOLEAN, 
	subtotal NUMERIC(15, 2), 
	tax_amount NUMERIC(15, 2), 
	discount_amount NUMERIC(15, 2), 
	total_amount NUMERIC(15, 2), 
	payment_method VARCHAR(13), 
	payment_date DATETIME, 
	payment_reference VARCHAR(100), 
	notes TEXT, 
	terms_and_conditions TEXT, 
	customer_id INTEGER NOT NULL, 
	user_id INTEGER NOT NULL, 
	id INTEGER NOT NULL, 
	created_at DATETIME NOT NULL, 
	updated_at DATETIME NOT NULL, 
	PRIMARY KEY (id), 
	FOREIGN KEY(customer_id) REFERENCES customers (id), 
	FOREIGN KEY(user_id) REFERENCES users (id)
)


2025-07-04 22:17:43,098 - sqlalchemy.engine.Engine - INFO - [no key 0.00013s] ()
2025-07-04 22:17:43,101 - sqlalchemy.engine.Engine - INFO - CREATE UNIQUE INDEX ix_invoices_invoice_number ON invoices (invoice_number)
2025-07-04 22:17:43,101 - sqlalchemy.engine.Engine - INFO - [no key 0.00011s] ()
2025-07-04 22:17:43,106 - sqlalchemy.engine.Engine - INFO - CREATE INDEX ix_invoices_id ON invoices (id)
2025-07-04 22:17:43,106 - sqlalchemy.engine.Engine - INFO - [no key 0.00010s] ()
2025-07-04 22:17:43,110 - sqlalchemy.engine.Engine - INFO - 
CREATE TABLE invoice_lines (
	invoice_id INTEGER NOT NULL, 
	product_id INTEGER NOT NULL, 
	description VARCHAR(200) NOT NULL, 
	quantity NUMERIC(15, 3), 
	unit_price NUMERIC(15, 2) NOT NULL, 
	tax_rate NUMERIC(5, 2), 
	discount_percent NUMERIC(5, 2), 
	id INTEGER NOT NULL, 
	created_at DATETIME NOT NULL, 
	updated_at DATETIME NOT NULL, 
	PRIMARY KEY (id), 
	FOREIGN KEY(invoice_id) REFERENCES invoices (id), 
	FOREIGN KEY(product_id) REFERENCES products (id)
)


2025-07-04 22:17:43,110 - sqlalchemy.engine.Engine - INFO - [no key 0.00019s] ()
2025-07-04 22:17:43,114 - sqlalchemy.engine.Engine - INFO - CREATE INDEX ix_invoice_lines_id ON invoice_lines (id)
2025-07-04 22:17:43,114 - sqlalchemy.engine.Engine - INFO - [no key 0.00011s] ()
2025-07-04 22:17:43,118 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-04 22:17:43,118 - __main__ - INFO - Database initialized successfully
2025-07-04 22:17:43,118 - __main__ - INFO - Creating application instance
2025-07-04 22:17:43,134 - __main__ - INFO - Creating main window
2025-07-05 20:21:45,361 - __main__ - INFO - Starting application initialization
2025-07-05 20:21:45,403 - config.settings - INFO - Application starting in development mode
2025-07-05 20:21:45,403 - config.settings - INFO - Database: sqlite:///./data/rabie_alkhaleej.db
2025-07-05 20:21:45,652 - __main__ - INFO - Initializing database...
2025-07-05 20:21:45,678 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-05 20:21:45,678 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("users")
2025-07-05 20:21:45,678 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-05 20:21:45,681 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("customers")
2025-07-05 20:21:45,681 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-05 20:21:45,681 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("product_categories")
2025-07-05 20:21:45,681 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-05 20:21:45,682 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("products")
2025-07-05 20:21:45,682 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-05 20:21:45,682 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("invoices")
2025-07-05 20:21:45,682 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-05 20:21:45,682 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("invoice_lines")
2025-07-05 20:21:45,683 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-05 20:21:45,683 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-05 20:21:45,684 - __main__ - INFO - Database initialized successfully
2025-07-05 20:21:45,684 - __main__ - INFO - Creating application instance
2025-07-05 20:21:45,701 - __main__ - INFO - Creating main window
2025-07-05 20:22:35,635 - __main__ - INFO - Starting application initialization
2025-07-05 20:22:35,637 - __main__ - ERROR - Import error: No module named 'PyQt6'
Traceback (most recent call last):
  File "C:\HamzaZareiProgram\RabieAlKhaleejApp\main.py", line 48, in main
    from PyQt6.QtWidgets import QApplication, QMessageBox
ModuleNotFoundError: No module named 'PyQt6'
2025-07-06 12:06:05,892 - __main__ - INFO - Starting application initialization
2025-07-06 12:06:05,892 - __main__ - ERROR - Import error: No module named 'PyQt6'
Traceback (most recent call last):
  File "C:\HamzaZareiProgram\RabieAlKhaleejApp\main.py", line 48, in main
    from PyQt6.QtWidgets import QApplication, QMessageBox
ModuleNotFoundError: No module named 'PyQt6'
2025-07-06 12:07:07,298 - __main__ - INFO - Starting application initialization
2025-07-06 12:07:07,411 - config.settings - INFO - Application starting in development mode
2025-07-06 12:07:07,411 - config.settings - INFO - Database: sqlite:///./data/rabie_alkhaleej.db
2025-07-06 12:07:07,738 - __main__ - INFO - Initializing database...
2025-07-06 12:07:07,764 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-06 12:07:07,764 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("users")
2025-07-06 12:07:07,764 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 12:07:07,768 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("customers")
2025-07-06 12:07:07,768 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 12:07:07,768 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("product_categories")
2025-07-06 12:07:07,769 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 12:07:07,769 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("products")
2025-07-06 12:07:07,769 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 12:07:07,770 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("invoices")
2025-07-06 12:07:07,770 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 12:07:07,770 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("invoice_lines")
2025-07-06 12:07:07,770 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 12:07:07,771 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-06 12:07:07,771 - __main__ - INFO - Database initialized successfully
2025-07-06 12:07:07,771 - __main__ - INFO - Creating application instance
2025-07-06 12:07:07,815 - __main__ - INFO - Creating main window
2025-07-06 12:21:41,607 - __main__ - INFO - Starting application initialization
2025-07-06 12:21:41,649 - config.settings - INFO - Application starting in development mode
2025-07-06 12:21:41,650 - config.settings - INFO - Database: sqlite:///./data/rabie_alkhaleej.db
2025-07-06 12:21:41,921 - __main__ - INFO - Initializing database...
2025-07-06 12:21:41,947 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-06 12:21:41,947 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("users")
2025-07-06 12:21:41,948 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 12:21:41,948 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("customers")
2025-07-06 12:21:41,949 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 12:21:41,949 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("product_categories")
2025-07-06 12:21:41,949 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 12:21:41,949 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("products")
2025-07-06 12:21:41,950 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 12:21:41,950 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("invoices")
2025-07-06 12:21:41,950 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 12:21:41,950 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("invoice_lines")
2025-07-06 12:21:41,950 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 12:21:41,951 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-06 12:21:41,951 - __main__ - INFO - Database initialized successfully
2025-07-06 12:21:41,951 - __main__ - INFO - Creating application instance
2025-07-06 12:21:41,971 - __main__ - INFO - Creating main window
2025-07-06 12:25:53,904 - __main__ - INFO - Starting application initialization
2025-07-06 12:25:53,947 - config.settings - INFO - Application starting in development mode
2025-07-06 12:25:53,947 - config.settings - INFO - Database: sqlite:///./data/rabie_alkhaleej.db
2025-07-06 12:25:54,179 - __main__ - INFO - Initializing database...
2025-07-06 12:25:54,200 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-06 12:25:54,200 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("users")
2025-07-06 12:25:54,200 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 12:25:54,201 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("customers")
2025-07-06 12:25:54,201 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 12:25:54,202 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("product_categories")
2025-07-06 12:25:54,202 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 12:25:54,203 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("products")
2025-07-06 12:25:54,203 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 12:25:54,203 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("invoices")
2025-07-06 12:25:54,203 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 12:25:54,204 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("invoice_lines")
2025-07-06 12:25:54,204 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 12:25:54,205 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-06 12:25:54,205 - __main__ - INFO - Database initialized successfully
2025-07-06 12:25:54,205 - __main__ - INFO - Creating application instance
2025-07-06 12:25:54,222 - __main__ - INFO - Creating main window
2025-07-06 12:25:54,223 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-06 12:25:54,224 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("users")
2025-07-06 12:25:54,224 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 12:25:54,224 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("customers")
2025-07-06 12:25:54,225 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 12:25:54,225 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("product_categories")
2025-07-06 12:25:54,225 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 12:25:54,226 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("products")
2025-07-06 12:25:54,226 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 12:25:54,226 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("invoices")
2025-07-06 12:25:54,226 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 12:25:54,227 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("invoice_lines")
2025-07-06 12:25:54,227 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 12:25:54,227 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-06 12:25:54,376 - __main__ - INFO - Starting application event loop
2025-07-06 12:31:31,836 - __main__ - INFO - Starting application initialization
2025-07-06 12:31:31,880 - config.settings - INFO - Application starting in development mode
2025-07-06 12:31:31,880 - config.settings - INFO - Database: sqlite:///./data/rabie_alkhaleej.db
2025-07-06 12:31:32,131 - __main__ - INFO - Initializing database...
2025-07-06 12:31:32,152 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-06 12:31:32,152 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("customers")
2025-07-06 12:31:32,153 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 12:31:32,153 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("users")
2025-07-06 12:31:32,153 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 12:31:32,154 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("product_categories")
2025-07-06 12:31:32,154 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 12:31:32,155 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("products")
2025-07-06 12:31:32,155 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 12:31:32,155 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("invoices")
2025-07-06 12:31:32,155 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 12:31:32,156 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("invoice_lines")
2025-07-06 12:31:32,156 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 12:31:32,156 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-06 12:31:32,156 - __main__ - INFO - Database initialized successfully
2025-07-06 12:31:32,157 - __main__ - INFO - Creating application instance
2025-07-06 12:31:32,174 - __main__ - INFO - Creating main window
2025-07-06 12:31:32,176 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-06 12:31:32,176 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("customers")
2025-07-06 12:31:32,176 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 12:31:32,177 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("users")
2025-07-06 12:31:32,177 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 12:31:32,178 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("product_categories")
2025-07-06 12:31:32,178 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 12:31:32,178 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("products")
2025-07-06 12:31:32,179 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 12:31:32,179 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("invoices")
2025-07-06 12:31:32,179 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 12:31:32,180 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("invoice_lines")
2025-07-06 12:31:32,180 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 12:31:32,181 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-06 12:31:32,254 - __main__ - INFO - Starting application event loop
2025-07-06 12:32:39,669 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-06 12:32:39,671 - sqlalchemy.engine.Engine - INFO - SELECT customers.name AS customers_name, customers.code AS customers_code, customers.contact_person AS customers_contact_person, customers.phone AS customers_phone, customers.mobile AS customers_mobile, customers.email AS customers_email, customers.address AS customers_address, customers.city AS customers_city, customers.state AS customers_state, customers.postal_code AS customers_postal_code, customers.country AS customers_country, customers.tax_id AS customers_tax_id, customers.credit_limit AS customers_credit_limit, customers.current_balance AS customers_current_balance, customers.notes AS customers_notes, customers.is_active AS customers_is_active, customers.id AS customers_id, customers.created_at AS customers_created_at, customers.updated_at AS customers_updated_at 
FROM customers ORDER BY customers.name
2025-07-06 12:32:39,671 - sqlalchemy.engine.Engine - INFO - [generated in 0.00047s] ()
2025-07-06 12:32:39,673 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-06 12:32:39,831 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-06 12:32:39,832 - sqlalchemy.engine.Engine - INFO - SELECT customers.name AS customers_name, customers.code AS customers_code, customers.contact_person AS customers_contact_person, customers.phone AS customers_phone, customers.mobile AS customers_mobile, customers.email AS customers_email, customers.address AS customers_address, customers.city AS customers_city, customers.state AS customers_state, customers.postal_code AS customers_postal_code, customers.country AS customers_country, customers.tax_id AS customers_tax_id, customers.credit_limit AS customers_credit_limit, customers.current_balance AS customers_current_balance, customers.notes AS customers_notes, customers.is_active AS customers_is_active, customers.id AS customers_id, customers.created_at AS customers_created_at, customers.updated_at AS customers_updated_at 
FROM customers ORDER BY customers.name
2025-07-06 12:32:39,832 - sqlalchemy.engine.Engine - INFO - [cached since 0.1609s ago] ()
2025-07-06 12:32:39,832 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-06 12:32:40,734 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-06 12:32:40,734 - sqlalchemy.engine.Engine - INFO - SELECT customers.name AS customers_name, customers.code AS customers_code, customers.contact_person AS customers_contact_person, customers.phone AS customers_phone, customers.mobile AS customers_mobile, customers.email AS customers_email, customers.address AS customers_address, customers.city AS customers_city, customers.state AS customers_state, customers.postal_code AS customers_postal_code, customers.country AS customers_country, customers.tax_id AS customers_tax_id, customers.credit_limit AS customers_credit_limit, customers.current_balance AS customers_current_balance, customers.notes AS customers_notes, customers.is_active AS customers_is_active, customers.id AS customers_id, customers.created_at AS customers_created_at, customers.updated_at AS customers_updated_at 
FROM customers ORDER BY customers.name
2025-07-06 12:32:40,734 - sqlalchemy.engine.Engine - INFO - [cached since 1.064s ago] ()
2025-07-06 12:32:40,736 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-06 12:33:15,320 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-06 12:33:15,321 - sqlalchemy.engine.Engine - INFO - SELECT customers.name AS customers_name, customers.code AS customers_code, customers.contact_person AS customers_contact_person, customers.phone AS customers_phone, customers.mobile AS customers_mobile, customers.email AS customers_email, customers.address AS customers_address, customers.city AS customers_city, customers.state AS customers_state, customers.postal_code AS customers_postal_code, customers.country AS customers_country, customers.tax_id AS customers_tax_id, customers.credit_limit AS customers_credit_limit, customers.current_balance AS customers_current_balance, customers.notes AS customers_notes, customers.is_active AS customers_is_active, customers.id AS customers_id, customers.created_at AS customers_created_at, customers.updated_at AS customers_updated_at 
FROM customers ORDER BY customers.name
2025-07-06 12:33:15,321 - sqlalchemy.engine.Engine - INFO - [cached since 35.65s ago] ()
2025-07-06 12:33:15,321 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-06 12:34:43,578 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-06 12:34:43,581 - sqlalchemy.engine.Engine - INFO - SELECT customers.name AS customers_name, customers.code AS customers_code, customers.contact_person AS customers_contact_person, customers.phone AS customers_phone, customers.mobile AS customers_mobile, customers.email AS customers_email, customers.address AS customers_address, customers.city AS customers_city, customers.state AS customers_state, customers.postal_code AS customers_postal_code, customers.country AS customers_country, customers.tax_id AS customers_tax_id, customers.credit_limit AS customers_credit_limit, customers.current_balance AS customers_current_balance, customers.notes AS customers_notes, customers.is_active AS customers_is_active, customers.id AS customers_id, customers.created_at AS customers_created_at, customers.updated_at AS customers_updated_at 
FROM customers 
WHERE customers.code = ?
 LIMIT ? OFFSET ?
2025-07-06 12:34:43,581 - sqlalchemy.engine.Engine - INFO - [generated in 0.00053s] ('1', 1, 0)
2025-07-06 12:34:43,584 - sqlalchemy.engine.Engine - INFO - INSERT INTO customers (name, code, contact_person, phone, mobile, email, address, city, state, postal_code, country, tax_id, credit_limit, current_balance, notes, is_active, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-07-06 12:34:43,584 - sqlalchemy.engine.Engine - INFO - [generated in 0.00051s] ('cyrus zarei', '1', None, '09981002103', '09900906130', '<EMAIL>', 'ya hooo', 'chaghal shahr', 'chaghaliyan', '1231544365', None, '125555', 999.0, 0.0, 'zesht', 1, '2025-07-06 09:04:43.583928', '2025-07-06 09:04:43.583928')
2025-07-06 12:34:43,586 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-06 12:34:44,742 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-06 12:34:44,744 - sqlalchemy.engine.Engine - INFO - SELECT customers.name AS customers_name, customers.code AS customers_code, customers.contact_person AS customers_contact_person, customers.phone AS customers_phone, customers.mobile AS customers_mobile, customers.email AS customers_email, customers.address AS customers_address, customers.city AS customers_city, customers.state AS customers_state, customers.postal_code AS customers_postal_code, customers.country AS customers_country, customers.tax_id AS customers_tax_id, customers.credit_limit AS customers_credit_limit, customers.current_balance AS customers_current_balance, customers.notes AS customers_notes, customers.is_active AS customers_is_active, customers.id AS customers_id, customers.created_at AS customers_created_at, customers.updated_at AS customers_updated_at 
FROM customers ORDER BY customers.name
2025-07-06 12:34:44,744 - sqlalchemy.engine.Engine - INFO - [cached since 125.1s ago] ()
2025-07-06 12:34:44,749 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-06 12:35:05,343 - __main__ - CRITICAL - Unhandled exception
Traceback (most recent call last):
  File "C:\HamzaZareiProgram\RabieAlKhaleejApp\modules\customer_management.py", line 479, in on_customer_selected
    self.show_customer_details(customer)
  File "C:\HamzaZareiProgram\RabieAlKhaleejApp\modules\customer_management.py", line 485, in show_customer_details
    self.details_layout.itemAt(i).widget().setParent(None)
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'setParent'
2025-07-06 12:35:05,895 - __main__ - CRITICAL - Unhandled exception
Traceback (most recent call last):
  File "C:\HamzaZareiProgram\RabieAlKhaleejApp\modules\customer_management.py", line 479, in on_customer_selected
    self.show_customer_details(customer)
  File "C:\HamzaZareiProgram\RabieAlKhaleejApp\modules\customer_management.py", line 485, in show_customer_details
    self.details_layout.itemAt(i).widget().setParent(None)
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'setParent'
2025-07-06 12:35:06,397 - __main__ - CRITICAL - Unhandled exception
Traceback (most recent call last):
  File "C:\HamzaZareiProgram\RabieAlKhaleejApp\modules\customer_management.py", line 479, in on_customer_selected
    self.show_customer_details(customer)
  File "C:\HamzaZareiProgram\RabieAlKhaleejApp\modules\customer_management.py", line 485, in show_customer_details
    self.details_layout.itemAt(i).widget().setParent(None)
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'setParent'
2025-07-06 12:35:06,817 - __main__ - CRITICAL - Unhandled exception
Traceback (most recent call last):
  File "C:\HamzaZareiProgram\RabieAlKhaleejApp\modules\customer_management.py", line 479, in on_customer_selected
    self.show_customer_details(customer)
  File "C:\HamzaZareiProgram\RabieAlKhaleejApp\modules\customer_management.py", line 485, in show_customer_details
    self.details_layout.itemAt(i).widget().setParent(None)
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'setParent'
2025-07-06 12:35:07,195 - __main__ - CRITICAL - Unhandled exception
Traceback (most recent call last):
  File "C:\HamzaZareiProgram\RabieAlKhaleejApp\modules\customer_management.py", line 479, in on_customer_selected
    self.show_customer_details(customer)
  File "C:\HamzaZareiProgram\RabieAlKhaleejApp\modules\customer_management.py", line 485, in show_customer_details
    self.details_layout.itemAt(i).widget().setParent(None)
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'setParent'
2025-07-06 12:35:07,605 - __main__ - CRITICAL - Unhandled exception
Traceback (most recent call last):
  File "C:\HamzaZareiProgram\RabieAlKhaleejApp\modules\customer_management.py", line 479, in on_customer_selected
    self.show_customer_details(customer)
  File "C:\HamzaZareiProgram\RabieAlKhaleejApp\modules\customer_management.py", line 485, in show_customer_details
    self.details_layout.itemAt(i).widget().setParent(None)
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'setParent'
2025-07-06 12:35:08,012 - __main__ - CRITICAL - Unhandled exception
Traceback (most recent call last):
  File "C:\HamzaZareiProgram\RabieAlKhaleejApp\modules\customer_management.py", line 479, in on_customer_selected
    self.show_customer_details(customer)
  File "C:\HamzaZareiProgram\RabieAlKhaleejApp\modules\customer_management.py", line 485, in show_customer_details
    self.details_layout.itemAt(i).widget().setParent(None)
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'setParent'
2025-07-06 12:35:08,961 - __main__ - CRITICAL - Unhandled exception
Traceback (most recent call last):
  File "C:\HamzaZareiProgram\RabieAlKhaleejApp\modules\customer_management.py", line 479, in on_customer_selected
    self.show_customer_details(customer)
  File "C:\HamzaZareiProgram\RabieAlKhaleejApp\modules\customer_management.py", line 485, in show_customer_details
    self.details_layout.itemAt(i).widget().setParent(None)
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'setParent'
2025-07-06 12:35:11,821 - __main__ - CRITICAL - Unhandled exception
Traceback (most recent call last):
  File "C:\HamzaZareiProgram\RabieAlKhaleejApp\modules\customer_management.py", line 479, in on_customer_selected
    self.show_customer_details(customer)
  File "C:\HamzaZareiProgram\RabieAlKhaleejApp\modules\customer_management.py", line 485, in show_customer_details
    self.details_layout.itemAt(i).widget().setParent(None)
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'setParent'
2025-07-06 12:35:24,167 - __main__ - CRITICAL - Unhandled exception
Traceback (most recent call last):
  File "C:\HamzaZareiProgram\RabieAlKhaleejApp\modules\customer_management.py", line 479, in on_customer_selected
    self.show_customer_details(customer)
  File "C:\HamzaZareiProgram\RabieAlKhaleejApp\modules\customer_management.py", line 485, in show_customer_details
    self.details_layout.itemAt(i).widget().setParent(None)
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'setParent'
2025-07-06 12:36:43,372 - __main__ - CRITICAL - Unhandled exception
Traceback (most recent call last):
  File "C:\HamzaZareiProgram\RabieAlKhaleejApp\modules\customer_management.py", line 479, in on_customer_selected
    self.show_customer_details(customer)
  File "C:\HamzaZareiProgram\RabieAlKhaleejApp\modules\customer_management.py", line 485, in show_customer_details
    self.details_layout.itemAt(i).widget().setParent(None)
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'setParent'
2025-07-06 12:36:44,359 - __main__ - CRITICAL - Unhandled exception
Traceback (most recent call last):
  File "C:\HamzaZareiProgram\RabieAlKhaleejApp\modules\customer_management.py", line 479, in on_customer_selected
    self.show_customer_details(customer)
  File "C:\HamzaZareiProgram\RabieAlKhaleejApp\modules\customer_management.py", line 485, in show_customer_details
    self.details_layout.itemAt(i).widget().setParent(None)
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'setParent'
2025-07-06 12:36:44,903 - __main__ - CRITICAL - Unhandled exception
Traceback (most recent call last):
  File "C:\HamzaZareiProgram\RabieAlKhaleejApp\modules\customer_management.py", line 479, in on_customer_selected
    self.show_customer_details(customer)
  File "C:\HamzaZareiProgram\RabieAlKhaleejApp\modules\customer_management.py", line 485, in show_customer_details
    self.details_layout.itemAt(i).widget().setParent(None)
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'setParent'
2025-07-06 12:36:49,117 - __main__ - CRITICAL - Unhandled exception
Traceback (most recent call last):
  File "C:\HamzaZareiProgram\RabieAlKhaleejApp\modules\customer_management.py", line 479, in on_customer_selected
    self.show_customer_details(customer)
  File "C:\HamzaZareiProgram\RabieAlKhaleejApp\modules\customer_management.py", line 485, in show_customer_details
    self.details_layout.itemAt(i).widget().setParent(None)
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'setParent'
2025-07-06 12:36:50,244 - __main__ - CRITICAL - Unhandled exception
Traceback (most recent call last):
  File "C:\HamzaZareiProgram\RabieAlKhaleejApp\modules\customer_management.py", line 479, in on_customer_selected
    self.show_customer_details(customer)
  File "C:\HamzaZareiProgram\RabieAlKhaleejApp\modules\customer_management.py", line 485, in show_customer_details
    self.details_layout.itemAt(i).widget().setParent(None)
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'setParent'
2025-07-06 12:36:56,661 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-06 12:36:56,661 - sqlalchemy.engine.Engine - INFO - SELECT customers.name AS customers_name, customers.code AS customers_code, customers.contact_person AS customers_contact_person, customers.phone AS customers_phone, customers.mobile AS customers_mobile, customers.email AS customers_email, customers.address AS customers_address, customers.city AS customers_city, customers.state AS customers_state, customers.postal_code AS customers_postal_code, customers.country AS customers_country, customers.tax_id AS customers_tax_id, customers.credit_limit AS customers_credit_limit, customers.current_balance AS customers_current_balance, customers.notes AS customers_notes, customers.is_active AS customers_is_active, customers.id AS customers_id, customers.created_at AS customers_created_at, customers.updated_at AS customers_updated_at 
FROM customers ORDER BY customers.name
2025-07-06 12:36:56,661 - sqlalchemy.engine.Engine - INFO - [cached since 257s ago] ()
2025-07-06 12:36:56,662 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-06 12:36:57,743 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-06 12:36:57,744 - sqlalchemy.engine.Engine - INFO - SELECT customers.name AS customers_name, customers.code AS customers_code, customers.contact_person AS customers_contact_person, customers.phone AS customers_phone, customers.mobile AS customers_mobile, customers.email AS customers_email, customers.address AS customers_address, customers.city AS customers_city, customers.state AS customers_state, customers.postal_code AS customers_postal_code, customers.country AS customers_country, customers.tax_id AS customers_tax_id, customers.credit_limit AS customers_credit_limit, customers.current_balance AS customers_current_balance, customers.notes AS customers_notes, customers.is_active AS customers_is_active, customers.id AS customers_id, customers.created_at AS customers_created_at, customers.updated_at AS customers_updated_at 
FROM customers ORDER BY customers.name
2025-07-06 12:36:57,744 - sqlalchemy.engine.Engine - INFO - [cached since 258.1s ago] ()
2025-07-06 12:36:57,745 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-06 12:36:57,937 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-06 12:36:57,937 - sqlalchemy.engine.Engine - INFO - SELECT customers.name AS customers_name, customers.code AS customers_code, customers.contact_person AS customers_contact_person, customers.phone AS customers_phone, customers.mobile AS customers_mobile, customers.email AS customers_email, customers.address AS customers_address, customers.city AS customers_city, customers.state AS customers_state, customers.postal_code AS customers_postal_code, customers.country AS customers_country, customers.tax_id AS customers_tax_id, customers.credit_limit AS customers_credit_limit, customers.current_balance AS customers_current_balance, customers.notes AS customers_notes, customers.is_active AS customers_is_active, customers.id AS customers_id, customers.created_at AS customers_created_at, customers.updated_at AS customers_updated_at 
FROM customers ORDER BY customers.name
2025-07-06 12:36:57,937 - sqlalchemy.engine.Engine - INFO - [cached since 258.3s ago] ()
2025-07-06 12:36:57,938 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-06 12:36:58,119 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-06 12:36:58,120 - sqlalchemy.engine.Engine - INFO - SELECT customers.name AS customers_name, customers.code AS customers_code, customers.contact_person AS customers_contact_person, customers.phone AS customers_phone, customers.mobile AS customers_mobile, customers.email AS customers_email, customers.address AS customers_address, customers.city AS customers_city, customers.state AS customers_state, customers.postal_code AS customers_postal_code, customers.country AS customers_country, customers.tax_id AS customers_tax_id, customers.credit_limit AS customers_credit_limit, customers.current_balance AS customers_current_balance, customers.notes AS customers_notes, customers.is_active AS customers_is_active, customers.id AS customers_id, customers.created_at AS customers_created_at, customers.updated_at AS customers_updated_at 
FROM customers ORDER BY customers.name
2025-07-06 12:36:58,120 - sqlalchemy.engine.Engine - INFO - [cached since 258.4s ago] ()
2025-07-06 12:36:58,121 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-06 12:36:58,305 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-06 12:36:58,305 - sqlalchemy.engine.Engine - INFO - SELECT customers.name AS customers_name, customers.code AS customers_code, customers.contact_person AS customers_contact_person, customers.phone AS customers_phone, customers.mobile AS customers_mobile, customers.email AS customers_email, customers.address AS customers_address, customers.city AS customers_city, customers.state AS customers_state, customers.postal_code AS customers_postal_code, customers.country AS customers_country, customers.tax_id AS customers_tax_id, customers.credit_limit AS customers_credit_limit, customers.current_balance AS customers_current_balance, customers.notes AS customers_notes, customers.is_active AS customers_is_active, customers.id AS customers_id, customers.created_at AS customers_created_at, customers.updated_at AS customers_updated_at 
FROM customers ORDER BY customers.name
2025-07-06 12:36:58,306 - sqlalchemy.engine.Engine - INFO - [cached since 258.6s ago] ()
2025-07-06 12:36:58,306 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-06 12:36:58,479 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-06 12:36:58,479 - sqlalchemy.engine.Engine - INFO - SELECT customers.name AS customers_name, customers.code AS customers_code, customers.contact_person AS customers_contact_person, customers.phone AS customers_phone, customers.mobile AS customers_mobile, customers.email AS customers_email, customers.address AS customers_address, customers.city AS customers_city, customers.state AS customers_state, customers.postal_code AS customers_postal_code, customers.country AS customers_country, customers.tax_id AS customers_tax_id, customers.credit_limit AS customers_credit_limit, customers.current_balance AS customers_current_balance, customers.notes AS customers_notes, customers.is_active AS customers_is_active, customers.id AS customers_id, customers.created_at AS customers_created_at, customers.updated_at AS customers_updated_at 
FROM customers ORDER BY customers.name
2025-07-06 12:36:58,479 - sqlalchemy.engine.Engine - INFO - [cached since 258.8s ago] ()
2025-07-06 12:36:58,480 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-06 12:36:58,653 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-06 12:36:58,654 - sqlalchemy.engine.Engine - INFO - SELECT customers.name AS customers_name, customers.code AS customers_code, customers.contact_person AS customers_contact_person, customers.phone AS customers_phone, customers.mobile AS customers_mobile, customers.email AS customers_email, customers.address AS customers_address, customers.city AS customers_city, customers.state AS customers_state, customers.postal_code AS customers_postal_code, customers.country AS customers_country, customers.tax_id AS customers_tax_id, customers.credit_limit AS customers_credit_limit, customers.current_balance AS customers_current_balance, customers.notes AS customers_notes, customers.is_active AS customers_is_active, customers.id AS customers_id, customers.created_at AS customers_created_at, customers.updated_at AS customers_updated_at 
FROM customers ORDER BY customers.name
2025-07-06 12:36:58,655 - sqlalchemy.engine.Engine - INFO - [cached since 259s ago] ()
2025-07-06 12:36:58,655 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-06 12:36:59,922 - __main__ - CRITICAL - Unhandled exception
Traceback (most recent call last):
  File "C:\HamzaZareiProgram\RabieAlKhaleejApp\modules\customer_management.py", line 479, in on_customer_selected
    self.show_customer_details(customer)
  File "C:\HamzaZareiProgram\RabieAlKhaleejApp\modules\customer_management.py", line 485, in show_customer_details
    self.details_layout.itemAt(i).widget().setParent(None)
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'setParent'
2025-07-06 12:37:01,045 - __main__ - CRITICAL - Unhandled exception
Traceback (most recent call last):
  File "C:\HamzaZareiProgram\RabieAlKhaleejApp\modules\customer_management.py", line 479, in on_customer_selected
    self.show_customer_details(customer)
  File "C:\HamzaZareiProgram\RabieAlKhaleejApp\modules\customer_management.py", line 485, in show_customer_details
    self.details_layout.itemAt(i).widget().setParent(None)
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'setParent'
2025-07-06 12:37:26,562 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-06 12:37:26,562 - sqlalchemy.engine.Engine - INFO - SELECT customers.name AS customers_name, customers.code AS customers_code, customers.contact_person AS customers_contact_person, customers.phone AS customers_phone, customers.mobile AS customers_mobile, customers.email AS customers_email, customers.address AS customers_address, customers.city AS customers_city, customers.state AS customers_state, customers.postal_code AS customers_postal_code, customers.country AS customers_country, customers.tax_id AS customers_tax_id, customers.credit_limit AS customers_credit_limit, customers.current_balance AS customers_current_balance, customers.notes AS customers_notes, customers.is_active AS customers_is_active, customers.id AS customers_id, customers.created_at AS customers_created_at, customers.updated_at AS customers_updated_at 
FROM customers 
WHERE customers.code = ? AND customers.id != ?
 LIMIT ? OFFSET ?
2025-07-06 12:37:26,562 - sqlalchemy.engine.Engine - INFO - [generated in 0.00039s] ('CUST009', 11, 1, 0)
2025-07-06 12:37:26,564 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-06 12:37:30,645 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-06 12:37:30,645 - sqlalchemy.engine.Engine - INFO - SELECT customers.name AS customers_name, customers.code AS customers_code, customers.contact_person AS customers_contact_person, customers.phone AS customers_phone, customers.mobile AS customers_mobile, customers.email AS customers_email, customers.address AS customers_address, customers.city AS customers_city, customers.state AS customers_state, customers.postal_code AS customers_postal_code, customers.country AS customers_country, customers.tax_id AS customers_tax_id, customers.credit_limit AS customers_credit_limit, customers.current_balance AS customers_current_balance, customers.notes AS customers_notes, customers.is_active AS customers_is_active, customers.id AS customers_id, customers.created_at AS customers_created_at, customers.updated_at AS customers_updated_at 
FROM customers ORDER BY customers.name
2025-07-06 12:37:30,646 - sqlalchemy.engine.Engine - INFO - [cached since 291s ago] ()
2025-07-06 12:37:30,646 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-06 12:37:30,915 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-06 12:37:30,915 - sqlalchemy.engine.Engine - INFO - SELECT customers.name AS customers_name, customers.code AS customers_code, customers.contact_person AS customers_contact_person, customers.phone AS customers_phone, customers.mobile AS customers_mobile, customers.email AS customers_email, customers.address AS customers_address, customers.city AS customers_city, customers.state AS customers_state, customers.postal_code AS customers_postal_code, customers.country AS customers_country, customers.tax_id AS customers_tax_id, customers.credit_limit AS customers_credit_limit, customers.current_balance AS customers_current_balance, customers.notes AS customers_notes, customers.is_active AS customers_is_active, customers.id AS customers_id, customers.created_at AS customers_created_at, customers.updated_at AS customers_updated_at 
FROM customers ORDER BY customers.name
2025-07-06 12:37:30,915 - sqlalchemy.engine.Engine - INFO - [cached since 291.2s ago] ()
2025-07-06 12:37:30,917 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-06 12:37:31,088 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-06 12:37:31,088 - sqlalchemy.engine.Engine - INFO - SELECT customers.name AS customers_name, customers.code AS customers_code, customers.contact_person AS customers_contact_person, customers.phone AS customers_phone, customers.mobile AS customers_mobile, customers.email AS customers_email, customers.address AS customers_address, customers.city AS customers_city, customers.state AS customers_state, customers.postal_code AS customers_postal_code, customers.country AS customers_country, customers.tax_id AS customers_tax_id, customers.credit_limit AS customers_credit_limit, customers.current_balance AS customers_current_balance, customers.notes AS customers_notes, customers.is_active AS customers_is_active, customers.id AS customers_id, customers.created_at AS customers_created_at, customers.updated_at AS customers_updated_at 
FROM customers ORDER BY customers.name
2025-07-06 12:37:31,088 - sqlalchemy.engine.Engine - INFO - [cached since 291.4s ago] ()
2025-07-06 12:37:31,090 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-06 12:37:31,274 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-06 12:37:31,274 - sqlalchemy.engine.Engine - INFO - SELECT customers.name AS customers_name, customers.code AS customers_code, customers.contact_person AS customers_contact_person, customers.phone AS customers_phone, customers.mobile AS customers_mobile, customers.email AS customers_email, customers.address AS customers_address, customers.city AS customers_city, customers.state AS customers_state, customers.postal_code AS customers_postal_code, customers.country AS customers_country, customers.tax_id AS customers_tax_id, customers.credit_limit AS customers_credit_limit, customers.current_balance AS customers_current_balance, customers.notes AS customers_notes, customers.is_active AS customers_is_active, customers.id AS customers_id, customers.created_at AS customers_created_at, customers.updated_at AS customers_updated_at 
FROM customers ORDER BY customers.name
2025-07-06 12:37:31,274 - sqlalchemy.engine.Engine - INFO - [cached since 291.6s ago] ()
2025-07-06 12:37:31,275 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-06 12:38:27,894 - __main__ - INFO - Starting application initialization
2025-07-06 12:38:27,935 - config.settings - INFO - Application starting in development mode
2025-07-06 12:38:27,935 - config.settings - INFO - Database: sqlite:///./data/rabie_alkhaleej.db
2025-07-06 12:38:28,183 - __main__ - INFO - Initializing database...
2025-07-06 12:38:28,184 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-06 12:38:28,184 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("users")
2025-07-06 12:38:28,184 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 12:38:28,185 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("customers")
2025-07-06 12:38:28,185 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 12:38:28,185 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("product_categories")
2025-07-06 12:38:28,185 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 12:38:28,186 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("products")
2025-07-06 12:38:28,186 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 12:38:28,186 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("invoices")
2025-07-06 12:38:28,187 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 12:38:28,187 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("invoice_lines")
2025-07-06 12:38:28,187 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 12:38:28,190 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-06 12:38:28,190 - __main__ - INFO - Database initialized successfully
2025-07-06 12:38:28,190 - __main__ - INFO - Creating application instance
2025-07-06 12:38:28,208 - __main__ - INFO - Creating main window
2025-07-06 12:38:28,208 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-06 12:38:28,209 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("users")
2025-07-06 12:38:28,209 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 12:38:28,210 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("customers")
2025-07-06 12:38:28,210 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 12:38:28,210 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("product_categories")
2025-07-06 12:38:28,210 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 12:38:28,211 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("products")
2025-07-06 12:38:28,211 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 12:38:28,211 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("invoices")
2025-07-06 12:38:28,211 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 12:38:28,212 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("invoice_lines")
2025-07-06 12:38:28,212 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 12:38:28,212 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-06 12:38:28,297 - __main__ - INFO - Starting application event loop
2025-07-06 12:38:30,902 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-06 12:38:30,904 - sqlalchemy.engine.Engine - INFO - SELECT customers.name AS customers_name, customers.code AS customers_code, customers.contact_person AS customers_contact_person, customers.phone AS customers_phone, customers.mobile AS customers_mobile, customers.email AS customers_email, customers.address AS customers_address, customers.city AS customers_city, customers.state AS customers_state, customers.postal_code AS customers_postal_code, customers.country AS customers_country, customers.tax_id AS customers_tax_id, customers.credit_limit AS customers_credit_limit, customers.current_balance AS customers_current_balance, customers.notes AS customers_notes, customers.is_active AS customers_is_active, customers.id AS customers_id, customers.created_at AS customers_created_at, customers.updated_at AS customers_updated_at 
FROM customers ORDER BY customers.name
2025-07-06 12:38:30,905 - sqlalchemy.engine.Engine - INFO - [generated in 0.00036s] ()
2025-07-06 12:38:30,905 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-06 12:38:42,400 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-06 12:38:42,402 - sqlalchemy.engine.Engine - INFO - SELECT customers.name AS customers_name, customers.code AS customers_code, customers.contact_person AS customers_contact_person, customers.phone AS customers_phone, customers.mobile AS customers_mobile, customers.email AS customers_email, customers.address AS customers_address, customers.city AS customers_city, customers.state AS customers_state, customers.postal_code AS customers_postal_code, customers.country AS customers_country, customers.tax_id AS customers_tax_id, customers.credit_limit AS customers_credit_limit, customers.current_balance AS customers_current_balance, customers.notes AS customers_notes, customers.is_active AS customers_is_active, customers.id AS customers_id, customers.created_at AS customers_created_at, customers.updated_at AS customers_updated_at 
FROM customers 
WHERE customers.code = ? AND customers.id != ?
 LIMIT ? OFFSET ?
2025-07-06 12:38:42,403 - sqlalchemy.engine.Engine - INFO - [generated in 0.00041s] ('CUST009', 11, 1, 0)
2025-07-06 12:38:42,403 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-06 12:39:43,515 - __main__ - CRITICAL - Unhandled exception
Traceback (most recent call last):
  File "C:\HamzaZareiProgram\RabieAlKhaleejApp\modules\customer_management.py", line 479, in on_customer_selected
    self.show_customer_details(customer)
  File "C:\HamzaZareiProgram\RabieAlKhaleejApp\modules\customer_management.py", line 485, in show_customer_details
    self.details_layout.itemAt(i).widget().setParent(None)
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'setParent'
2025-07-06 12:39:44,001 - __main__ - CRITICAL - Unhandled exception
Traceback (most recent call last):
  File "C:\HamzaZareiProgram\RabieAlKhaleejApp\modules\customer_management.py", line 479, in on_customer_selected
    self.show_customer_details(customer)
  File "C:\HamzaZareiProgram\RabieAlKhaleejApp\modules\customer_management.py", line 485, in show_customer_details
    self.details_layout.itemAt(i).widget().setParent(None)
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'setParent'
2025-07-06 12:40:33,904 - __main__ - CRITICAL - Unhandled exception
Traceback (most recent call last):
  File "C:\HamzaZareiProgram\RabieAlKhaleejApp\modules\customer_management.py", line 479, in on_customer_selected
    self.show_customer_details(customer)
  File "C:\HamzaZareiProgram\RabieAlKhaleejApp\modules\customer_management.py", line 485, in show_customer_details
    self.details_layout.itemAt(i).widget().setParent(None)
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'setParent'
2025-07-06 12:40:38,324 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-06 12:40:38,325 - sqlalchemy.engine.Engine - INFO - SELECT customers.name AS customers_name, customers.code AS customers_code, customers.contact_person AS customers_contact_person, customers.phone AS customers_phone, customers.mobile AS customers_mobile, customers.email AS customers_email, customers.address AS customers_address, customers.city AS customers_city, customers.state AS customers_state, customers.postal_code AS customers_postal_code, customers.country AS customers_country, customers.tax_id AS customers_tax_id, customers.credit_limit AS customers_credit_limit, customers.current_balance AS customers_current_balance, customers.notes AS customers_notes, customers.is_active AS customers_is_active, customers.id AS customers_id, customers.created_at AS customers_created_at, customers.updated_at AS customers_updated_at 
FROM customers ORDER BY customers.name
2025-07-06 12:40:38,325 - sqlalchemy.engine.Engine - INFO - [cached since 127.4s ago] ()
2025-07-06 12:40:38,326 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-06 12:40:38,878 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-06 12:40:38,878 - sqlalchemy.engine.Engine - INFO - SELECT customers.name AS customers_name, customers.code AS customers_code, customers.contact_person AS customers_contact_person, customers.phone AS customers_phone, customers.mobile AS customers_mobile, customers.email AS customers_email, customers.address AS customers_address, customers.city AS customers_city, customers.state AS customers_state, customers.postal_code AS customers_postal_code, customers.country AS customers_country, customers.tax_id AS customers_tax_id, customers.credit_limit AS customers_credit_limit, customers.current_balance AS customers_current_balance, customers.notes AS customers_notes, customers.is_active AS customers_is_active, customers.id AS customers_id, customers.created_at AS customers_created_at, customers.updated_at AS customers_updated_at 
FROM customers ORDER BY customers.name
2025-07-06 12:40:38,878 - sqlalchemy.engine.Engine - INFO - [cached since 128s ago] ()
2025-07-06 12:40:38,879 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-06 12:40:39,053 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-06 12:40:39,053 - sqlalchemy.engine.Engine - INFO - SELECT customers.name AS customers_name, customers.code AS customers_code, customers.contact_person AS customers_contact_person, customers.phone AS customers_phone, customers.mobile AS customers_mobile, customers.email AS customers_email, customers.address AS customers_address, customers.city AS customers_city, customers.state AS customers_state, customers.postal_code AS customers_postal_code, customers.country AS customers_country, customers.tax_id AS customers_tax_id, customers.credit_limit AS customers_credit_limit, customers.current_balance AS customers_current_balance, customers.notes AS customers_notes, customers.is_active AS customers_is_active, customers.id AS customers_id, customers.created_at AS customers_created_at, customers.updated_at AS customers_updated_at 
FROM customers ORDER BY customers.name
2025-07-06 12:40:39,053 - sqlalchemy.engine.Engine - INFO - [cached since 128.1s ago] ()
2025-07-06 12:40:39,055 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-06 12:40:39,215 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-06 12:40:39,215 - sqlalchemy.engine.Engine - INFO - SELECT customers.name AS customers_name, customers.code AS customers_code, customers.contact_person AS customers_contact_person, customers.phone AS customers_phone, customers.mobile AS customers_mobile, customers.email AS customers_email, customers.address AS customers_address, customers.city AS customers_city, customers.state AS customers_state, customers.postal_code AS customers_postal_code, customers.country AS customers_country, customers.tax_id AS customers_tax_id, customers.credit_limit AS customers_credit_limit, customers.current_balance AS customers_current_balance, customers.notes AS customers_notes, customers.is_active AS customers_is_active, customers.id AS customers_id, customers.created_at AS customers_created_at, customers.updated_at AS customers_updated_at 
FROM customers ORDER BY customers.name
2025-07-06 12:40:39,215 - sqlalchemy.engine.Engine - INFO - [cached since 128.3s ago] ()
2025-07-06 12:40:39,215 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-06 12:40:39,388 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-06 12:40:39,388 - sqlalchemy.engine.Engine - INFO - SELECT customers.name AS customers_name, customers.code AS customers_code, customers.contact_person AS customers_contact_person, customers.phone AS customers_phone, customers.mobile AS customers_mobile, customers.email AS customers_email, customers.address AS customers_address, customers.city AS customers_city, customers.state AS customers_state, customers.postal_code AS customers_postal_code, customers.country AS customers_country, customers.tax_id AS customers_tax_id, customers.credit_limit AS customers_credit_limit, customers.current_balance AS customers_current_balance, customers.notes AS customers_notes, customers.is_active AS customers_is_active, customers.id AS customers_id, customers.created_at AS customers_created_at, customers.updated_at AS customers_updated_at 
FROM customers ORDER BY customers.name
2025-07-06 12:40:39,388 - sqlalchemy.engine.Engine - INFO - [cached since 128.5s ago] ()
2025-07-06 12:40:39,389 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-06 12:40:41,579 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-06 12:40:41,581 - sqlalchemy.engine.Engine - INFO - SELECT customers.name AS customers_name, customers.code AS customers_code, customers.contact_person AS customers_contact_person, customers.phone AS customers_phone, customers.mobile AS customers_mobile, customers.email AS customers_email, customers.address AS customers_address, customers.city AS customers_city, customers.state AS customers_state, customers.postal_code AS customers_postal_code, customers.country AS customers_country, customers.tax_id AS customers_tax_id, customers.credit_limit AS customers_credit_limit, customers.current_balance AS customers_current_balance, customers.notes AS customers_notes, customers.is_active AS customers_is_active, customers.id AS customers_id, customers.created_at AS customers_created_at, customers.updated_at AS customers_updated_at 
FROM customers ORDER BY customers.name
2025-07-06 12:40:41,581 - sqlalchemy.engine.Engine - INFO - [cached since 130.7s ago] ()
2025-07-06 12:40:41,582 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-06 12:40:41,747 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-06 12:40:41,747 - sqlalchemy.engine.Engine - INFO - SELECT customers.name AS customers_name, customers.code AS customers_code, customers.contact_person AS customers_contact_person, customers.phone AS customers_phone, customers.mobile AS customers_mobile, customers.email AS customers_email, customers.address AS customers_address, customers.city AS customers_city, customers.state AS customers_state, customers.postal_code AS customers_postal_code, customers.country AS customers_country, customers.tax_id AS customers_tax_id, customers.credit_limit AS customers_credit_limit, customers.current_balance AS customers_current_balance, customers.notes AS customers_notes, customers.is_active AS customers_is_active, customers.id AS customers_id, customers.created_at AS customers_created_at, customers.updated_at AS customers_updated_at 
FROM customers ORDER BY customers.name
2025-07-06 12:40:41,747 - sqlalchemy.engine.Engine - INFO - [cached since 130.8s ago] ()
2025-07-06 12:40:41,749 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-06 12:40:41,931 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-06 12:40:41,932 - sqlalchemy.engine.Engine - INFO - SELECT customers.name AS customers_name, customers.code AS customers_code, customers.contact_person AS customers_contact_person, customers.phone AS customers_phone, customers.mobile AS customers_mobile, customers.email AS customers_email, customers.address AS customers_address, customers.city AS customers_city, customers.state AS customers_state, customers.postal_code AS customers_postal_code, customers.country AS customers_country, customers.tax_id AS customers_tax_id, customers.credit_limit AS customers_credit_limit, customers.current_balance AS customers_current_balance, customers.notes AS customers_notes, customers.is_active AS customers_is_active, customers.id AS customers_id, customers.created_at AS customers_created_at, customers.updated_at AS customers_updated_at 
FROM customers ORDER BY customers.name
2025-07-06 12:40:41,932 - sqlalchemy.engine.Engine - INFO - [cached since 131s ago] ()
2025-07-06 12:40:41,932 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-06 12:40:42,085 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-06 12:40:42,085 - sqlalchemy.engine.Engine - INFO - SELECT customers.name AS customers_name, customers.code AS customers_code, customers.contact_person AS customers_contact_person, customers.phone AS customers_phone, customers.mobile AS customers_mobile, customers.email AS customers_email, customers.address AS customers_address, customers.city AS customers_city, customers.state AS customers_state, customers.postal_code AS customers_postal_code, customers.country AS customers_country, customers.tax_id AS customers_tax_id, customers.credit_limit AS customers_credit_limit, customers.current_balance AS customers_current_balance, customers.notes AS customers_notes, customers.is_active AS customers_is_active, customers.id AS customers_id, customers.created_at AS customers_created_at, customers.updated_at AS customers_updated_at 
FROM customers ORDER BY customers.name
2025-07-06 12:40:42,085 - sqlalchemy.engine.Engine - INFO - [cached since 131.2s ago] ()
2025-07-06 12:40:42,086 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-06 12:43:04,217 - __main__ - CRITICAL - Unhandled exception
Traceback (most recent call last):
  File "C:\HamzaZareiProgram\RabieAlKhaleejApp\modules\customer_management.py", line 479, in on_customer_selected
    self.show_customer_details(customer)
  File "C:\HamzaZareiProgram\RabieAlKhaleejApp\modules\customer_management.py", line 485, in show_customer_details
    child = self.details_layout.takeAt(0)
AttributeError: 'NoneType' object has no attribute 'setParent'
2025-07-06 12:43:16,089 - __main__ - INFO - Starting application initialization
2025-07-06 12:43:16,129 - config.settings - INFO - Application starting in development mode
2025-07-06 12:43:16,129 - config.settings - INFO - Database: sqlite:///./data/rabie_alkhaleej.db
2025-07-06 12:43:16,388 - __main__ - INFO - Initializing database...
2025-07-06 12:43:16,388 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-06 12:43:16,389 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("users")
2025-07-06 12:43:16,389 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 12:43:16,390 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("customers")
2025-07-06 12:43:16,390 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 12:43:16,390 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("product_categories")
2025-07-06 12:43:16,390 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 12:43:16,390 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("products")
2025-07-06 12:43:16,390 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 12:43:16,392 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("invoices")
2025-07-06 12:43:16,392 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 12:43:16,392 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("invoice_lines")
2025-07-06 12:43:16,392 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 12:43:16,393 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-06 12:43:16,393 - __main__ - INFO - Database initialized successfully
2025-07-06 12:43:16,393 - __main__ - INFO - Creating application instance
2025-07-06 12:43:16,409 - __main__ - INFO - Creating main window
2025-07-06 12:43:16,412 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-06 12:43:16,412 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("users")
2025-07-06 12:43:16,412 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 12:43:16,412 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("customers")
2025-07-06 12:43:16,412 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 12:43:16,412 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("product_categories")
2025-07-06 12:43:16,412 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 12:43:16,413 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("products")
2025-07-06 12:43:16,414 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 12:43:16,414 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("invoices")
2025-07-06 12:43:16,414 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 12:43:16,414 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("invoice_lines")
2025-07-06 12:43:16,414 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 12:43:16,415 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-06 12:43:16,505 - __main__ - INFO - Starting application event loop
2025-07-06 12:43:17,945 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-06 12:43:17,948 - sqlalchemy.engine.Engine - INFO - SELECT customers.name AS customers_name, customers.code AS customers_code, customers.contact_person AS customers_contact_person, customers.phone AS customers_phone, customers.mobile AS customers_mobile, customers.email AS customers_email, customers.address AS customers_address, customers.city AS customers_city, customers.state AS customers_state, customers.postal_code AS customers_postal_code, customers.country AS customers_country, customers.tax_id AS customers_tax_id, customers.credit_limit AS customers_credit_limit, customers.current_balance AS customers_current_balance, customers.notes AS customers_notes, customers.is_active AS customers_is_active, customers.id AS customers_id, customers.created_at AS customers_created_at, customers.updated_at AS customers_updated_at 
FROM customers ORDER BY customers.name
2025-07-06 12:43:17,948 - sqlalchemy.engine.Engine - INFO - [generated in 0.00040s] ()
2025-07-06 12:43:17,949 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-06 12:47:33,345 - __main__ - INFO - Starting application initialization
2025-07-06 12:47:33,384 - config.settings - INFO - Application starting in development mode
2025-07-06 12:47:33,384 - config.settings - INFO - Database: sqlite:///./data/rabie_alkhaleej.db
2025-07-06 12:47:33,639 - __main__ - INFO - Initializing database...
2025-07-06 12:47:33,641 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-06 12:47:33,641 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("users")
2025-07-06 12:47:33,641 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 12:47:33,642 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("customers")
2025-07-06 12:47:33,642 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 12:47:33,642 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("product_categories")
2025-07-06 12:47:33,643 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 12:47:33,643 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("products")
2025-07-06 12:47:33,643 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 12:47:33,644 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("invoices")
2025-07-06 12:47:33,644 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 12:47:33,644 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("invoice_lines")
2025-07-06 12:47:33,645 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 12:47:33,645 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-06 12:47:33,645 - __main__ - INFO - Database initialized successfully
2025-07-06 12:47:33,646 - __main__ - INFO - Creating application instance
2025-07-06 12:47:33,666 - __main__ - INFO - Creating main window
2025-07-06 12:47:33,668 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-06 12:47:33,668 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("users")
2025-07-06 12:47:33,668 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 12:47:33,669 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("customers")
2025-07-06 12:47:33,669 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 12:47:33,670 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("product_categories")
2025-07-06 12:47:33,670 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 12:47:33,670 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("products")
2025-07-06 12:47:33,670 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 12:47:33,671 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("invoices")
2025-07-06 12:47:33,671 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 12:47:33,672 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("invoice_lines")
2025-07-06 12:47:33,672 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 12:47:33,672 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-06 12:47:33,749 - __main__ - INFO - Starting application event loop
2025-07-06 12:47:35,758 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-06 12:47:35,761 - sqlalchemy.engine.Engine - INFO - SELECT customers.name AS customers_name, customers.code AS customers_code, customers.contact_person AS customers_contact_person, customers.phone AS customers_phone, customers.mobile AS customers_mobile, customers.email AS customers_email, customers.address AS customers_address, customers.city AS customers_city, customers.state AS customers_state, customers.postal_code AS customers_postal_code, customers.country AS customers_country, customers.tax_id AS customers_tax_id, customers.credit_limit AS customers_credit_limit, customers.current_balance AS customers_current_balance, customers.notes AS customers_notes, customers.is_active AS customers_is_active, customers.id AS customers_id, customers.created_at AS customers_created_at, customers.updated_at AS customers_updated_at 
FROM customers ORDER BY customers.name
2025-07-06 12:47:35,761 - sqlalchemy.engine.Engine - INFO - [generated in 0.00046s] ()
2025-07-06 12:47:35,762 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-06 12:48:32,847 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-06 12:48:32,849 - sqlalchemy.engine.Engine - INFO - SELECT customers.name AS customers_name, customers.code AS customers_code, customers.contact_person AS customers_contact_person, customers.phone AS customers_phone, customers.mobile AS customers_mobile, customers.email AS customers_email, customers.address AS customers_address, customers.city AS customers_city, customers.state AS customers_state, customers.postal_code AS customers_postal_code, customers.country AS customers_country, customers.tax_id AS customers_tax_id, customers.credit_limit AS customers_credit_limit, customers.current_balance AS customers_current_balance, customers.notes AS customers_notes, customers.is_active AS customers_is_active, customers.id AS customers_id, customers.created_at AS customers_created_at, customers.updated_at AS customers_updated_at 
FROM customers 
WHERE customers.code = ? AND customers.id != ?
 LIMIT ? OFFSET ?
2025-07-06 12:48:32,849 - sqlalchemy.engine.Engine - INFO - [generated in 0.00051s] ('CUST009', 11, 1, 0)
2025-07-06 12:48:34,340 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-06 12:48:55,471 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-06 12:48:55,472 - sqlalchemy.engine.Engine - INFO - SELECT customers.name AS customers_name, customers.code AS customers_code, customers.contact_person AS customers_contact_person, customers.phone AS customers_phone, customers.mobile AS customers_mobile, customers.email AS customers_email, customers.address AS customers_address, customers.city AS customers_city, customers.state AS customers_state, customers.postal_code AS customers_postal_code, customers.country AS customers_country, customers.tax_id AS customers_tax_id, customers.credit_limit AS customers_credit_limit, customers.current_balance AS customers_current_balance, customers.notes AS customers_notes, customers.is_active AS customers_is_active, customers.id AS customers_id, customers.created_at AS customers_created_at, customers.updated_at AS customers_updated_at 
FROM customers ORDER BY customers.name
2025-07-06 12:48:55,472 - sqlalchemy.engine.Engine - INFO - [cached since 79.71s ago] ()
2025-07-06 12:48:55,473 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-06 12:49:15,477 - __main__ - INFO - Starting application initialization
2025-07-06 12:49:15,516 - config.settings - INFO - Application starting in development mode
2025-07-06 12:49:15,516 - config.settings - INFO - Database: sqlite:///./data/rabie_alkhaleej.db
2025-07-06 12:49:15,769 - __main__ - INFO - Initializing database...
2025-07-06 12:49:15,770 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-06 12:49:15,770 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("users")
2025-07-06 12:49:15,771 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 12:49:15,771 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("customers")
2025-07-06 12:49:15,772 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 12:49:15,772 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("product_categories")
2025-07-06 12:49:15,772 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 12:49:15,772 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("products")
2025-07-06 12:49:15,772 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 12:49:15,772 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("invoices")
2025-07-06 12:49:15,772 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 12:49:15,772 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("invoice_lines")
2025-07-06 12:49:15,772 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 12:49:15,772 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-06 12:49:15,772 - __main__ - INFO - Database initialized successfully
2025-07-06 12:49:15,775 - __main__ - INFO - Creating application instance
2025-07-06 12:49:15,792 - __main__ - INFO - Creating main window
2025-07-06 12:49:15,793 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-06 12:49:15,793 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("users")
2025-07-06 12:49:15,793 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 12:49:15,793 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("customers")
2025-07-06 12:49:15,793 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 12:49:15,794 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("product_categories")
2025-07-06 12:49:15,794 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 12:49:15,794 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("products")
2025-07-06 12:49:15,795 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 12:49:15,795 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("invoices")
2025-07-06 12:49:15,795 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 12:49:15,795 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("invoice_lines")
2025-07-06 12:49:15,795 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 12:49:15,796 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-06 12:49:15,888 - __main__ - INFO - Starting application event loop
2025-07-06 12:49:17,930 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-06 12:49:17,932 - sqlalchemy.engine.Engine - INFO - SELECT customers.name AS customers_name, customers.code AS customers_code, customers.contact_person AS customers_contact_person, customers.phone AS customers_phone, customers.mobile AS customers_mobile, customers.email AS customers_email, customers.address AS customers_address, customers.city AS customers_city, customers.state AS customers_state, customers.postal_code AS customers_postal_code, customers.country AS customers_country, customers.tax_id AS customers_tax_id, customers.credit_limit AS customers_credit_limit, customers.current_balance AS customers_current_balance, customers.notes AS customers_notes, customers.is_active AS customers_is_active, customers.id AS customers_id, customers.created_at AS customers_created_at, customers.updated_at AS customers_updated_at 
FROM customers ORDER BY customers.name
2025-07-06 12:49:17,932 - sqlalchemy.engine.Engine - INFO - [generated in 0.00039s] ()
2025-07-06 12:49:17,934 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-06 12:49:30,433 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-06 12:49:30,434 - sqlalchemy.engine.Engine - INFO - SELECT customers.name AS customers_name, customers.code AS customers_code, customers.contact_person AS customers_contact_person, customers.phone AS customers_phone, customers.mobile AS customers_mobile, customers.email AS customers_email, customers.address AS customers_address, customers.city AS customers_city, customers.state AS customers_state, customers.postal_code AS customers_postal_code, customers.country AS customers_country, customers.tax_id AS customers_tax_id, customers.credit_limit AS customers_credit_limit, customers.current_balance AS customers_current_balance, customers.notes AS customers_notes, customers.is_active AS customers_is_active, customers.id AS customers_id, customers.created_at AS customers_created_at, customers.updated_at AS customers_updated_at 
FROM customers 
WHERE customers.id = ?
2025-07-06 12:49:30,434 - sqlalchemy.engine.Engine - INFO - [generated in 0.00022s] (11,)
2025-07-06 12:49:30,436 - sqlalchemy.engine.Engine - INFO - SELECT invoices.invoice_number AS invoices_invoice_number, invoices.invoice_date AS invoices_invoice_date, invoices.due_date AS invoices_due_date, invoices.status AS invoices_status, invoices.is_paid AS invoices_is_paid, invoices.subtotal AS invoices_subtotal, invoices.tax_amount AS invoices_tax_amount, invoices.discount_amount AS invoices_discount_amount, invoices.total_amount AS invoices_total_amount, invoices.payment_method AS invoices_payment_method, invoices.payment_date AS invoices_payment_date, invoices.payment_reference AS invoices_payment_reference, invoices.notes AS invoices_notes, invoices.terms_and_conditions AS invoices_terms_and_conditions, invoices.customer_id AS invoices_customer_id, invoices.user_id AS invoices_user_id, invoices.id AS invoices_id, invoices.created_at AS invoices_created_at, invoices.updated_at AS invoices_updated_at 
FROM invoices 
WHERE ? = invoices.customer_id
2025-07-06 12:49:30,437 - sqlalchemy.engine.Engine - INFO - [generated in 0.00027s] (11,)
2025-07-06 12:49:30,438 - sqlalchemy.engine.Engine - INFO - DELETE FROM customers WHERE customers.id = ?
2025-07-06 12:49:30,438 - sqlalchemy.engine.Engine - INFO - [generated in 0.00026s] (11,)
2025-07-06 12:49:30,440 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-06 12:49:31,584 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-06 12:49:31,584 - sqlalchemy.engine.Engine - INFO - SELECT customers.name AS customers_name, customers.code AS customers_code, customers.contact_person AS customers_contact_person, customers.phone AS customers_phone, customers.mobile AS customers_mobile, customers.email AS customers_email, customers.address AS customers_address, customers.city AS customers_city, customers.state AS customers_state, customers.postal_code AS customers_postal_code, customers.country AS customers_country, customers.tax_id AS customers_tax_id, customers.credit_limit AS customers_credit_limit, customers.current_balance AS customers_current_balance, customers.notes AS customers_notes, customers.is_active AS customers_is_active, customers.id AS customers_id, customers.created_at AS customers_created_at, customers.updated_at AS customers_updated_at 
FROM customers ORDER BY customers.name
2025-07-06 12:49:31,584 - sqlalchemy.engine.Engine - INFO - [cached since 13.65s ago] ()
2025-07-06 12:49:31,586 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-06 12:49:32,978 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-06 12:49:32,978 - sqlalchemy.engine.Engine - INFO - SELECT customers.name AS customers_name, customers.code AS customers_code, customers.contact_person AS customers_contact_person, customers.phone AS customers_phone, customers.mobile AS customers_mobile, customers.email AS customers_email, customers.address AS customers_address, customers.city AS customers_city, customers.state AS customers_state, customers.postal_code AS customers_postal_code, customers.country AS customers_country, customers.tax_id AS customers_tax_id, customers.credit_limit AS customers_credit_limit, customers.current_balance AS customers_current_balance, customers.notes AS customers_notes, customers.is_active AS customers_is_active, customers.id AS customers_id, customers.created_at AS customers_created_at, customers.updated_at AS customers_updated_at 
FROM customers ORDER BY customers.name
2025-07-06 12:49:32,979 - sqlalchemy.engine.Engine - INFO - [cached since 15.05s ago] ()
2025-07-06 12:49:32,979 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-06 12:49:33,153 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-06 12:49:33,154 - sqlalchemy.engine.Engine - INFO - SELECT customers.name AS customers_name, customers.code AS customers_code, customers.contact_person AS customers_contact_person, customers.phone AS customers_phone, customers.mobile AS customers_mobile, customers.email AS customers_email, customers.address AS customers_address, customers.city AS customers_city, customers.state AS customers_state, customers.postal_code AS customers_postal_code, customers.country AS customers_country, customers.tax_id AS customers_tax_id, customers.credit_limit AS customers_credit_limit, customers.current_balance AS customers_current_balance, customers.notes AS customers_notes, customers.is_active AS customers_is_active, customers.id AS customers_id, customers.created_at AS customers_created_at, customers.updated_at AS customers_updated_at 
FROM customers ORDER BY customers.name
2025-07-06 12:49:33,154 - sqlalchemy.engine.Engine - INFO - [cached since 15.22s ago] ()
2025-07-06 12:49:33,154 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-06 12:49:33,327 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-06 12:49:33,328 - sqlalchemy.engine.Engine - INFO - SELECT customers.name AS customers_name, customers.code AS customers_code, customers.contact_person AS customers_contact_person, customers.phone AS customers_phone, customers.mobile AS customers_mobile, customers.email AS customers_email, customers.address AS customers_address, customers.city AS customers_city, customers.state AS customers_state, customers.postal_code AS customers_postal_code, customers.country AS customers_country, customers.tax_id AS customers_tax_id, customers.credit_limit AS customers_credit_limit, customers.current_balance AS customers_current_balance, customers.notes AS customers_notes, customers.is_active AS customers_is_active, customers.id AS customers_id, customers.created_at AS customers_created_at, customers.updated_at AS customers_updated_at 
FROM customers ORDER BY customers.name
2025-07-06 12:49:33,328 - sqlalchemy.engine.Engine - INFO - [cached since 15.4s ago] ()
2025-07-06 12:49:33,328 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-06 12:49:33,499 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-06 12:49:33,499 - sqlalchemy.engine.Engine - INFO - SELECT customers.name AS customers_name, customers.code AS customers_code, customers.contact_person AS customers_contact_person, customers.phone AS customers_phone, customers.mobile AS customers_mobile, customers.email AS customers_email, customers.address AS customers_address, customers.city AS customers_city, customers.state AS customers_state, customers.postal_code AS customers_postal_code, customers.country AS customers_country, customers.tax_id AS customers_tax_id, customers.credit_limit AS customers_credit_limit, customers.current_balance AS customers_current_balance, customers.notes AS customers_notes, customers.is_active AS customers_is_active, customers.id AS customers_id, customers.created_at AS customers_created_at, customers.updated_at AS customers_updated_at 
FROM customers ORDER BY customers.name
2025-07-06 12:49:33,499 - sqlalchemy.engine.Engine - INFO - [cached since 15.57s ago] ()
2025-07-06 12:49:33,501 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-06 12:49:33,652 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-06 12:49:33,653 - sqlalchemy.engine.Engine - INFO - SELECT customers.name AS customers_name, customers.code AS customers_code, customers.contact_person AS customers_contact_person, customers.phone AS customers_phone, customers.mobile AS customers_mobile, customers.email AS customers_email, customers.address AS customers_address, customers.city AS customers_city, customers.state AS customers_state, customers.postal_code AS customers_postal_code, customers.country AS customers_country, customers.tax_id AS customers_tax_id, customers.credit_limit AS customers_credit_limit, customers.current_balance AS customers_current_balance, customers.notes AS customers_notes, customers.is_active AS customers_is_active, customers.id AS customers_id, customers.created_at AS customers_created_at, customers.updated_at AS customers_updated_at 
FROM customers ORDER BY customers.name
2025-07-06 12:49:33,653 - sqlalchemy.engine.Engine - INFO - [cached since 15.72s ago] ()
2025-07-06 12:49:33,653 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-06 12:50:45,098 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-06 12:50:45,099 - sqlalchemy.engine.Engine - INFO - SELECT customers.name AS customers_name, customers.code AS customers_code, customers.contact_person AS customers_contact_person, customers.phone AS customers_phone, customers.mobile AS customers_mobile, customers.email AS customers_email, customers.address AS customers_address, customers.city AS customers_city, customers.state AS customers_state, customers.postal_code AS customers_postal_code, customers.country AS customers_country, customers.tax_id AS customers_tax_id, customers.credit_limit AS customers_credit_limit, customers.current_balance AS customers_current_balance, customers.notes AS customers_notes, customers.is_active AS customers_is_active, customers.id AS customers_id, customers.created_at AS customers_created_at, customers.updated_at AS customers_updated_at 
FROM customers 
WHERE customers.code = ?
 LIMIT ? OFFSET ?
2025-07-06 12:50:45,100 - sqlalchemy.engine.Engine - INFO - [generated in 0.00024s] ('CUST010', 1, 0)
2025-07-06 12:50:49,014 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-06 12:50:53,448 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-06 12:50:53,448 - sqlalchemy.engine.Engine - INFO - SELECT customers.name AS customers_name, customers.code AS customers_code, customers.contact_person AS customers_contact_person, customers.phone AS customers_phone, customers.mobile AS customers_mobile, customers.email AS customers_email, customers.address AS customers_address, customers.city AS customers_city, customers.state AS customers_state, customers.postal_code AS customers_postal_code, customers.country AS customers_country, customers.tax_id AS customers_tax_id, customers.credit_limit AS customers_credit_limit, customers.current_balance AS customers_current_balance, customers.notes AS customers_notes, customers.is_active AS customers_is_active, customers.id AS customers_id, customers.created_at AS customers_created_at, customers.updated_at AS customers_updated_at 
FROM customers ORDER BY customers.name
2025-07-06 12:50:53,449 - sqlalchemy.engine.Engine - INFO - [cached since 95.52s ago] ()
2025-07-06 12:50:53,449 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-06 12:50:56,531 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-06 12:50:56,531 - sqlalchemy.engine.Engine - INFO - SELECT customers.name AS customers_name, customers.code AS customers_code, customers.contact_person AS customers_contact_person, customers.phone AS customers_phone, customers.mobile AS customers_mobile, customers.email AS customers_email, customers.address AS customers_address, customers.city AS customers_city, customers.state AS customers_state, customers.postal_code AS customers_postal_code, customers.country AS customers_country, customers.tax_id AS customers_tax_id, customers.credit_limit AS customers_credit_limit, customers.current_balance AS customers_current_balance, customers.notes AS customers_notes, customers.is_active AS customers_is_active, customers.id AS customers_id, customers.created_at AS customers_created_at, customers.updated_at AS customers_updated_at 
FROM customers ORDER BY customers.name
2025-07-06 12:50:56,532 - sqlalchemy.engine.Engine - INFO - [cached since 98.6s ago] ()
2025-07-06 12:50:56,533 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-06 12:50:56,971 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-06 12:50:56,971 - sqlalchemy.engine.Engine - INFO - SELECT customers.name AS customers_name, customers.code AS customers_code, customers.contact_person AS customers_contact_person, customers.phone AS customers_phone, customers.mobile AS customers_mobile, customers.email AS customers_email, customers.address AS customers_address, customers.city AS customers_city, customers.state AS customers_state, customers.postal_code AS customers_postal_code, customers.country AS customers_country, customers.tax_id AS customers_tax_id, customers.credit_limit AS customers_credit_limit, customers.current_balance AS customers_current_balance, customers.notes AS customers_notes, customers.is_active AS customers_is_active, customers.id AS customers_id, customers.created_at AS customers_created_at, customers.updated_at AS customers_updated_at 
FROM customers ORDER BY customers.name
2025-07-06 12:50:56,973 - sqlalchemy.engine.Engine - INFO - [cached since 99.04s ago] ()
2025-07-06 12:50:56,973 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-06 12:50:57,363 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-06 12:50:57,364 - sqlalchemy.engine.Engine - INFO - SELECT customers.name AS customers_name, customers.code AS customers_code, customers.contact_person AS customers_contact_person, customers.phone AS customers_phone, customers.mobile AS customers_mobile, customers.email AS customers_email, customers.address AS customers_address, customers.city AS customers_city, customers.state AS customers_state, customers.postal_code AS customers_postal_code, customers.country AS customers_country, customers.tax_id AS customers_tax_id, customers.credit_limit AS customers_credit_limit, customers.current_balance AS customers_current_balance, customers.notes AS customers_notes, customers.is_active AS customers_is_active, customers.id AS customers_id, customers.created_at AS customers_created_at, customers.updated_at AS customers_updated_at 
FROM customers ORDER BY customers.name
2025-07-06 12:50:57,364 - sqlalchemy.engine.Engine - INFO - [cached since 99.43s ago] ()
2025-07-06 12:50:57,365 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-06 12:50:57,536 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-06 12:50:57,537 - sqlalchemy.engine.Engine - INFO - SELECT customers.name AS customers_name, customers.code AS customers_code, customers.contact_person AS customers_contact_person, customers.phone AS customers_phone, customers.mobile AS customers_mobile, customers.email AS customers_email, customers.address AS customers_address, customers.city AS customers_city, customers.state AS customers_state, customers.postal_code AS customers_postal_code, customers.country AS customers_country, customers.tax_id AS customers_tax_id, customers.credit_limit AS customers_credit_limit, customers.current_balance AS customers_current_balance, customers.notes AS customers_notes, customers.is_active AS customers_is_active, customers.id AS customers_id, customers.created_at AS customers_created_at, customers.updated_at AS customers_updated_at 
FROM customers ORDER BY customers.name
2025-07-06 12:50:57,537 - sqlalchemy.engine.Engine - INFO - [cached since 99.61s ago] ()
2025-07-06 12:50:57,537 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-06 12:50:57,709 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-06 12:50:57,710 - sqlalchemy.engine.Engine - INFO - SELECT customers.name AS customers_name, customers.code AS customers_code, customers.contact_person AS customers_contact_person, customers.phone AS customers_phone, customers.mobile AS customers_mobile, customers.email AS customers_email, customers.address AS customers_address, customers.city AS customers_city, customers.state AS customers_state, customers.postal_code AS customers_postal_code, customers.country AS customers_country, customers.tax_id AS customers_tax_id, customers.credit_limit AS customers_credit_limit, customers.current_balance AS customers_current_balance, customers.notes AS customers_notes, customers.is_active AS customers_is_active, customers.id AS customers_id, customers.created_at AS customers_created_at, customers.updated_at AS customers_updated_at 
FROM customers ORDER BY customers.name
2025-07-06 12:50:57,710 - sqlalchemy.engine.Engine - INFO - [cached since 99.78s ago] ()
2025-07-06 12:50:57,710 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-06 12:50:57,877 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-06 12:50:57,878 - sqlalchemy.engine.Engine - INFO - SELECT customers.name AS customers_name, customers.code AS customers_code, customers.contact_person AS customers_contact_person, customers.phone AS customers_phone, customers.mobile AS customers_mobile, customers.email AS customers_email, customers.address AS customers_address, customers.city AS customers_city, customers.state AS customers_state, customers.postal_code AS customers_postal_code, customers.country AS customers_country, customers.tax_id AS customers_tax_id, customers.credit_limit AS customers_credit_limit, customers.current_balance AS customers_current_balance, customers.notes AS customers_notes, customers.is_active AS customers_is_active, customers.id AS customers_id, customers.created_at AS customers_created_at, customers.updated_at AS customers_updated_at 
FROM customers ORDER BY customers.name
2025-07-06 12:50:57,878 - sqlalchemy.engine.Engine - INFO - [cached since 99.95s ago] ()
2025-07-06 12:50:57,879 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-06 12:50:58,041 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-06 12:50:58,041 - sqlalchemy.engine.Engine - INFO - SELECT customers.name AS customers_name, customers.code AS customers_code, customers.contact_person AS customers_contact_person, customers.phone AS customers_phone, customers.mobile AS customers_mobile, customers.email AS customers_email, customers.address AS customers_address, customers.city AS customers_city, customers.state AS customers_state, customers.postal_code AS customers_postal_code, customers.country AS customers_country, customers.tax_id AS customers_tax_id, customers.credit_limit AS customers_credit_limit, customers.current_balance AS customers_current_balance, customers.notes AS customers_notes, customers.is_active AS customers_is_active, customers.id AS customers_id, customers.created_at AS customers_created_at, customers.updated_at AS customers_updated_at 
FROM customers ORDER BY customers.name
2025-07-06 12:50:58,041 - sqlalchemy.engine.Engine - INFO - [cached since 100.1s ago] ()
2025-07-06 12:50:58,042 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-06 12:50:58,212 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-06 12:50:58,213 - sqlalchemy.engine.Engine - INFO - SELECT customers.name AS customers_name, customers.code AS customers_code, customers.contact_person AS customers_contact_person, customers.phone AS customers_phone, customers.mobile AS customers_mobile, customers.email AS customers_email, customers.address AS customers_address, customers.city AS customers_city, customers.state AS customers_state, customers.postal_code AS customers_postal_code, customers.country AS customers_country, customers.tax_id AS customers_tax_id, customers.credit_limit AS customers_credit_limit, customers.current_balance AS customers_current_balance, customers.notes AS customers_notes, customers.is_active AS customers_is_active, customers.id AS customers_id, customers.created_at AS customers_created_at, customers.updated_at AS customers_updated_at 
FROM customers ORDER BY customers.name
2025-07-06 12:50:58,213 - sqlalchemy.engine.Engine - INFO - [cached since 100.3s ago] ()
2025-07-06 12:50:58,214 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-06 12:50:58,391 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-06 12:50:58,392 - sqlalchemy.engine.Engine - INFO - SELECT customers.name AS customers_name, customers.code AS customers_code, customers.contact_person AS customers_contact_person, customers.phone AS customers_phone, customers.mobile AS customers_mobile, customers.email AS customers_email, customers.address AS customers_address, customers.city AS customers_city, customers.state AS customers_state, customers.postal_code AS customers_postal_code, customers.country AS customers_country, customers.tax_id AS customers_tax_id, customers.credit_limit AS customers_credit_limit, customers.current_balance AS customers_current_balance, customers.notes AS customers_notes, customers.is_active AS customers_is_active, customers.id AS customers_id, customers.created_at AS customers_created_at, customers.updated_at AS customers_updated_at 
FROM customers ORDER BY customers.name
2025-07-06 12:50:58,392 - sqlalchemy.engine.Engine - INFO - [cached since 100.5s ago] ()
2025-07-06 12:50:58,392 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-06 12:50:58,552 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-06 12:50:58,553 - sqlalchemy.engine.Engine - INFO - SELECT customers.name AS customers_name, customers.code AS customers_code, customers.contact_person AS customers_contact_person, customers.phone AS customers_phone, customers.mobile AS customers_mobile, customers.email AS customers_email, customers.address AS customers_address, customers.city AS customers_city, customers.state AS customers_state, customers.postal_code AS customers_postal_code, customers.country AS customers_country, customers.tax_id AS customers_tax_id, customers.credit_limit AS customers_credit_limit, customers.current_balance AS customers_current_balance, customers.notes AS customers_notes, customers.is_active AS customers_is_active, customers.id AS customers_id, customers.created_at AS customers_created_at, customers.updated_at AS customers_updated_at 
FROM customers ORDER BY customers.name
2025-07-06 12:50:58,553 - sqlalchemy.engine.Engine - INFO - [cached since 100.6s ago] ()
2025-07-06 12:50:58,553 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-06 12:50:58,719 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-06 12:50:58,719 - sqlalchemy.engine.Engine - INFO - SELECT customers.name AS customers_name, customers.code AS customers_code, customers.contact_person AS customers_contact_person, customers.phone AS customers_phone, customers.mobile AS customers_mobile, customers.email AS customers_email, customers.address AS customers_address, customers.city AS customers_city, customers.state AS customers_state, customers.postal_code AS customers_postal_code, customers.country AS customers_country, customers.tax_id AS customers_tax_id, customers.credit_limit AS customers_credit_limit, customers.current_balance AS customers_current_balance, customers.notes AS customers_notes, customers.is_active AS customers_is_active, customers.id AS customers_id, customers.created_at AS customers_created_at, customers.updated_at AS customers_updated_at 
FROM customers ORDER BY customers.name
2025-07-06 12:50:58,720 - sqlalchemy.engine.Engine - INFO - [cached since 100.8s ago] ()
2025-07-06 12:50:58,720 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-06 12:50:58,893 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-06 12:50:58,893 - sqlalchemy.engine.Engine - INFO - SELECT customers.name AS customers_name, customers.code AS customers_code, customers.contact_person AS customers_contact_person, customers.phone AS customers_phone, customers.mobile AS customers_mobile, customers.email AS customers_email, customers.address AS customers_address, customers.city AS customers_city, customers.state AS customers_state, customers.postal_code AS customers_postal_code, customers.country AS customers_country, customers.tax_id AS customers_tax_id, customers.credit_limit AS customers_credit_limit, customers.current_balance AS customers_current_balance, customers.notes AS customers_notes, customers.is_active AS customers_is_active, customers.id AS customers_id, customers.created_at AS customers_created_at, customers.updated_at AS customers_updated_at 
FROM customers ORDER BY customers.name
2025-07-06 12:50:58,894 - sqlalchemy.engine.Engine - INFO - [cached since 101s ago] ()
2025-07-06 12:50:58,894 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-06 12:50:59,061 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-06 12:50:59,061 - sqlalchemy.engine.Engine - INFO - SELECT customers.name AS customers_name, customers.code AS customers_code, customers.contact_person AS customers_contact_person, customers.phone AS customers_phone, customers.mobile AS customers_mobile, customers.email AS customers_email, customers.address AS customers_address, customers.city AS customers_city, customers.state AS customers_state, customers.postal_code AS customers_postal_code, customers.country AS customers_country, customers.tax_id AS customers_tax_id, customers.credit_limit AS customers_credit_limit, customers.current_balance AS customers_current_balance, customers.notes AS customers_notes, customers.is_active AS customers_is_active, customers.id AS customers_id, customers.created_at AS customers_created_at, customers.updated_at AS customers_updated_at 
FROM customers ORDER BY customers.name
2025-07-06 12:50:59,062 - sqlalchemy.engine.Engine - INFO - [cached since 101.1s ago] ()
2025-07-06 12:50:59,062 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-06 12:50:59,228 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-06 12:50:59,228 - sqlalchemy.engine.Engine - INFO - SELECT customers.name AS customers_name, customers.code AS customers_code, customers.contact_person AS customers_contact_person, customers.phone AS customers_phone, customers.mobile AS customers_mobile, customers.email AS customers_email, customers.address AS customers_address, customers.city AS customers_city, customers.state AS customers_state, customers.postal_code AS customers_postal_code, customers.country AS customers_country, customers.tax_id AS customers_tax_id, customers.credit_limit AS customers_credit_limit, customers.current_balance AS customers_current_balance, customers.notes AS customers_notes, customers.is_active AS customers_is_active, customers.id AS customers_id, customers.created_at AS customers_created_at, customers.updated_at AS customers_updated_at 
FROM customers ORDER BY customers.name
2025-07-06 12:50:59,228 - sqlalchemy.engine.Engine - INFO - [cached since 101.3s ago] ()
2025-07-06 12:50:59,229 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-06 12:50:59,400 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-06 12:50:59,400 - sqlalchemy.engine.Engine - INFO - SELECT customers.name AS customers_name, customers.code AS customers_code, customers.contact_person AS customers_contact_person, customers.phone AS customers_phone, customers.mobile AS customers_mobile, customers.email AS customers_email, customers.address AS customers_address, customers.city AS customers_city, customers.state AS customers_state, customers.postal_code AS customers_postal_code, customers.country AS customers_country, customers.tax_id AS customers_tax_id, customers.credit_limit AS customers_credit_limit, customers.current_balance AS customers_current_balance, customers.notes AS customers_notes, customers.is_active AS customers_is_active, customers.id AS customers_id, customers.created_at AS customers_created_at, customers.updated_at AS customers_updated_at 
FROM customers ORDER BY customers.name
2025-07-06 12:50:59,400 - sqlalchemy.engine.Engine - INFO - [cached since 101.5s ago] ()
2025-07-06 12:50:59,401 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-06 12:50:59,570 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-06 12:50:59,571 - sqlalchemy.engine.Engine - INFO - SELECT customers.name AS customers_name, customers.code AS customers_code, customers.contact_person AS customers_contact_person, customers.phone AS customers_phone, customers.mobile AS customers_mobile, customers.email AS customers_email, customers.address AS customers_address, customers.city AS customers_city, customers.state AS customers_state, customers.postal_code AS customers_postal_code, customers.country AS customers_country, customers.tax_id AS customers_tax_id, customers.credit_limit AS customers_credit_limit, customers.current_balance AS customers_current_balance, customers.notes AS customers_notes, customers.is_active AS customers_is_active, customers.id AS customers_id, customers.created_at AS customers_created_at, customers.updated_at AS customers_updated_at 
FROM customers ORDER BY customers.name
2025-07-06 12:50:59,571 - sqlalchemy.engine.Engine - INFO - [cached since 101.6s ago] ()
2025-07-06 12:50:59,571 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-06 12:50:59,742 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-06 12:50:59,742 - sqlalchemy.engine.Engine - INFO - SELECT customers.name AS customers_name, customers.code AS customers_code, customers.contact_person AS customers_contact_person, customers.phone AS customers_phone, customers.mobile AS customers_mobile, customers.email AS customers_email, customers.address AS customers_address, customers.city AS customers_city, customers.state AS customers_state, customers.postal_code AS customers_postal_code, customers.country AS customers_country, customers.tax_id AS customers_tax_id, customers.credit_limit AS customers_credit_limit, customers.current_balance AS customers_current_balance, customers.notes AS customers_notes, customers.is_active AS customers_is_active, customers.id AS customers_id, customers.created_at AS customers_created_at, customers.updated_at AS customers_updated_at 
FROM customers ORDER BY customers.name
2025-07-06 12:50:59,742 - sqlalchemy.engine.Engine - INFO - [cached since 101.8s ago] ()
2025-07-06 12:50:59,743 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-06 12:50:59,911 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-06 12:50:59,911 - sqlalchemy.engine.Engine - INFO - SELECT customers.name AS customers_name, customers.code AS customers_code, customers.contact_person AS customers_contact_person, customers.phone AS customers_phone, customers.mobile AS customers_mobile, customers.email AS customers_email, customers.address AS customers_address, customers.city AS customers_city, customers.state AS customers_state, customers.postal_code AS customers_postal_code, customers.country AS customers_country, customers.tax_id AS customers_tax_id, customers.credit_limit AS customers_credit_limit, customers.current_balance AS customers_current_balance, customers.notes AS customers_notes, customers.is_active AS customers_is_active, customers.id AS customers_id, customers.created_at AS customers_created_at, customers.updated_at AS customers_updated_at 
FROM customers ORDER BY customers.name
2025-07-06 12:50:59,911 - sqlalchemy.engine.Engine - INFO - [cached since 102s ago] ()
2025-07-06 12:50:59,912 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-06 12:51:00,094 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-06 12:51:00,094 - sqlalchemy.engine.Engine - INFO - SELECT customers.name AS customers_name, customers.code AS customers_code, customers.contact_person AS customers_contact_person, customers.phone AS customers_phone, customers.mobile AS customers_mobile, customers.email AS customers_email, customers.address AS customers_address, customers.city AS customers_city, customers.state AS customers_state, customers.postal_code AS customers_postal_code, customers.country AS customers_country, customers.tax_id AS customers_tax_id, customers.credit_limit AS customers_credit_limit, customers.current_balance AS customers_current_balance, customers.notes AS customers_notes, customers.is_active AS customers_is_active, customers.id AS customers_id, customers.created_at AS customers_created_at, customers.updated_at AS customers_updated_at 
FROM customers ORDER BY customers.name
2025-07-06 12:51:00,094 - sqlalchemy.engine.Engine - INFO - [cached since 102.2s ago] ()
2025-07-06 12:51:00,095 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-06 12:53:25,516 - __main__ - INFO - Starting application initialization
2025-07-06 12:53:25,555 - config.settings - INFO - Application starting in development mode
2025-07-06 12:53:25,555 - config.settings - INFO - Database: sqlite:///./data/rabie_alkhaleej.db
2025-07-06 12:53:25,806 - __main__ - INFO - Initializing database...
2025-07-06 12:53:25,807 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-06 12:53:25,807 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("users")
2025-07-06 12:53:25,808 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 12:53:25,808 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("customers")
2025-07-06 12:53:25,809 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 12:53:25,809 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("product_categories")
2025-07-06 12:53:25,809 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 12:53:25,809 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("products")
2025-07-06 12:53:25,809 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 12:53:25,809 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("invoices")
2025-07-06 12:53:25,810 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 12:53:25,810 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("invoice_lines")
2025-07-06 12:53:25,810 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 12:53:25,810 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-06 12:53:25,811 - __main__ - INFO - Database initialized successfully
2025-07-06 12:53:25,811 - __main__ - INFO - Creating application instance
2025-07-06 12:53:25,827 - __main__ - INFO - Creating main window
2025-07-06 12:53:25,828 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-06 12:53:25,828 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("users")
2025-07-06 12:53:25,828 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 12:53:25,829 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("customers")
2025-07-06 12:53:25,829 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 12:53:25,829 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("product_categories")
2025-07-06 12:53:25,829 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 12:53:25,830 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("products")
2025-07-06 12:53:25,830 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 12:53:25,830 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("invoices")
2025-07-06 12:53:25,831 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 12:53:25,831 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("invoice_lines")
2025-07-06 12:53:25,831 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 12:53:25,831 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-06 12:53:25,938 - __main__ - INFO - Starting application event loop
2025-07-06 12:53:28,164 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-06 12:53:28,166 - sqlalchemy.engine.Engine - INFO - SELECT customers.name AS customers_name, customers.contact_person AS customers_contact_person, customers.phone AS customers_phone, customers.mobile AS customers_mobile, customers.email AS customers_email, customers.address AS customers_address, customers.city AS customers_city, customers.state AS customers_state, customers.postal_code AS customers_postal_code, customers.country AS customers_country, customers.tax_id AS customers_tax_id, customers.credit_limit AS customers_credit_limit, customers.current_balance AS customers_current_balance, customers.notes AS customers_notes, customers.id AS customers_id, customers.created_at AS customers_created_at, customers.updated_at AS customers_updated_at 
FROM customers ORDER BY customers.name
2025-07-06 12:53:28,167 - sqlalchemy.engine.Engine - INFO - [generated in 0.00043s] ()
2025-07-06 12:53:28,168 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-06 12:53:28,168 - modules.customer_management - ERROR - Error loading customers: 'Customer' object has no attribute 'code'
2025-07-06 12:55:50,803 - __main__ - INFO - Starting application initialization
2025-07-06 12:55:50,841 - config.settings - INFO - Application starting in development mode
2025-07-06 12:55:50,841 - config.settings - INFO - Database: sqlite:///./data/rabie_alkhaleej.db
2025-07-06 12:55:51,102 - __main__ - INFO - Initializing database...
2025-07-06 12:55:51,104 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-06 12:55:51,104 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("users")
2025-07-06 12:55:51,104 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 12:55:51,105 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("customers")
2025-07-06 12:55:51,105 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 12:55:51,106 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("product_categories")
2025-07-06 12:55:51,106 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 12:55:51,106 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("products")
2025-07-06 12:55:51,106 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 12:55:51,107 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("invoices")
2025-07-06 12:55:51,107 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 12:55:51,107 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("invoice_lines")
2025-07-06 12:55:51,107 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 12:55:51,108 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-06 12:55:51,108 - __main__ - INFO - Database initialized successfully
2025-07-06 12:55:51,108 - __main__ - INFO - Creating application instance
2025-07-06 12:55:51,124 - __main__ - INFO - Creating main window
2025-07-06 12:55:51,125 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-06 12:55:51,125 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("users")
2025-07-06 12:55:51,125 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 12:55:51,126 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("customers")
2025-07-06 12:55:51,127 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 12:55:51,127 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("product_categories")
2025-07-06 12:55:51,127 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 12:55:51,127 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("products")
2025-07-06 12:55:51,127 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 12:55:51,128 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("invoices")
2025-07-06 12:55:51,128 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 12:55:51,128 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("invoice_lines")
2025-07-06 12:55:51,128 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 12:55:51,128 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-06 12:55:51,217 - __main__ - INFO - Starting application event loop
2025-07-06 12:55:52,771 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-06 12:55:52,773 - sqlalchemy.engine.Engine - INFO - SELECT customers.name AS customers_name, customers.contact_person AS customers_contact_person, customers.phone AS customers_phone, customers.mobile AS customers_mobile, customers.email AS customers_email, customers.address AS customers_address, customers.city AS customers_city, customers.state AS customers_state, customers.postal_code AS customers_postal_code, customers.country AS customers_country, customers.tax_id AS customers_tax_id, customers.credit_limit AS customers_credit_limit, customers.current_balance AS customers_current_balance, customers.notes AS customers_notes, customers.id AS customers_id, customers.created_at AS customers_created_at, customers.updated_at AS customers_updated_at 
FROM customers ORDER BY customers.name
2025-07-06 12:55:52,773 - sqlalchemy.engine.Engine - INFO - [generated in 0.00036s] ()
2025-07-06 12:55:52,774 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-06 12:55:52,774 - modules.customer_management - ERROR - Error loading customers: 'Customer' object has no attribute 'code'
2025-07-06 12:58:37,194 - __main__ - INFO - Starting application initialization
2025-07-06 12:58:37,230 - config.settings - INFO - Application starting in development mode
2025-07-06 12:58:37,230 - config.settings - INFO - Database: sqlite:///./data/rabie_alkhaleej.db
2025-07-06 12:58:37,488 - __main__ - INFO - Initializing database...
2025-07-06 12:58:37,490 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-06 12:58:37,490 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("users")
2025-07-06 12:58:37,490 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 12:58:37,491 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("customers")
2025-07-06 12:58:37,491 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 12:58:37,491 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("product_categories")
2025-07-06 12:58:37,491 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 12:58:37,491 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("products")
2025-07-06 12:58:37,491 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 12:58:37,493 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("invoices")
2025-07-06 12:58:37,493 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 12:58:37,493 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("invoice_lines")
2025-07-06 12:58:37,493 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 12:58:37,494 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-06 12:58:37,494 - __main__ - INFO - Database initialized successfully
2025-07-06 12:58:37,494 - __main__ - INFO - Creating application instance
2025-07-06 12:58:37,511 - __main__ - INFO - Creating main window
2025-07-06 12:58:37,512 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-06 12:58:37,512 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("users")
2025-07-06 12:58:37,512 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 12:58:37,512 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("customers")
2025-07-06 12:58:37,512 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 12:58:37,514 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("product_categories")
2025-07-06 12:58:37,514 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 12:58:37,514 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("products")
2025-07-06 12:58:37,514 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 12:58:37,514 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("invoices")
2025-07-06 12:58:37,515 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 12:58:37,515 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("invoice_lines")
2025-07-06 12:58:37,515 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 12:58:37,515 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-06 12:58:37,609 - __main__ - INFO - Starting application event loop
2025-07-06 12:58:38,786 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-06 12:58:38,789 - sqlalchemy.engine.Engine - INFO - SELECT customers.name AS customers_name, customers.contact_person AS customers_contact_person, customers.phone AS customers_phone, customers.mobile AS customers_mobile, customers.email AS customers_email, customers.address AS customers_address, customers.city AS customers_city, customers.state AS customers_state, customers.postal_code AS customers_postal_code, customers.country AS customers_country, customers.tax_id AS customers_tax_id, customers.credit_limit AS customers_credit_limit, customers.current_balance AS customers_current_balance, customers.notes AS customers_notes, customers.id AS customers_id, customers.created_at AS customers_created_at, customers.updated_at AS customers_updated_at 
FROM customers ORDER BY customers.name
2025-07-06 12:58:38,789 - sqlalchemy.engine.Engine - INFO - [generated in 0.00029s] ()
2025-07-06 12:58:38,789 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-06 13:00:17,584 - __main__ - INFO - Starting application initialization
2025-07-06 13:00:17,623 - config.settings - INFO - Application starting in development mode
2025-07-06 13:00:17,623 - config.settings - INFO - Database: sqlite:///./data/rabie_alkhaleej.db
2025-07-06 13:00:17,882 - __main__ - INFO - Initializing database...
2025-07-06 13:00:17,882 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-06 13:00:17,883 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("users")
2025-07-06 13:00:17,883 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 13:00:17,884 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("customers")
2025-07-06 13:00:17,884 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 13:00:17,884 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("product_categories")
2025-07-06 13:00:17,885 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 13:00:17,885 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("products")
2025-07-06 13:00:17,885 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 13:00:17,885 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("invoices")
2025-07-06 13:00:17,885 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 13:00:17,886 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("invoice_lines")
2025-07-06 13:00:17,886 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 13:00:17,886 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-06 13:00:17,887 - __main__ - INFO - Database initialized successfully
2025-07-06 13:00:17,887 - __main__ - INFO - Creating application instance
2025-07-06 13:00:17,904 - __main__ - INFO - Creating main window
2025-07-06 13:00:17,905 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-06 13:00:17,905 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("users")
2025-07-06 13:00:17,905 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 13:00:17,906 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("customers")
2025-07-06 13:00:17,906 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 13:00:17,906 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("product_categories")
2025-07-06 13:00:17,906 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 13:00:17,906 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("products")
2025-07-06 13:00:17,907 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 13:00:17,907 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("invoices")
2025-07-06 13:00:17,907 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 13:00:17,907 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("invoice_lines")
2025-07-06 13:00:17,907 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 13:00:17,908 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-06 13:00:17,998 - __main__ - INFO - Starting application event loop
2025-07-06 13:02:02,766 - __main__ - INFO - Starting application initialization
2025-07-06 13:02:02,804 - config.settings - INFO - Application starting in development mode
2025-07-06 13:02:02,804 - config.settings - INFO - Database: sqlite:///./data/rabie_alkhaleej.db
2025-07-06 13:02:03,058 - __main__ - INFO - Initializing database...
2025-07-06 13:02:03,058 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-06 13:02:03,059 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("users")
2025-07-06 13:02:03,059 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 13:02:03,060 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("customers")
2025-07-06 13:02:03,060 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 13:02:03,060 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("product_categories")
2025-07-06 13:02:03,060 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 13:02:03,060 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("products")
2025-07-06 13:02:03,062 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 13:02:03,062 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("invoices")
2025-07-06 13:02:03,062 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 13:02:03,063 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("invoice_lines")
2025-07-06 13:02:03,063 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 13:02:03,063 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-06 13:02:03,063 - __main__ - INFO - Database initialized successfully
2025-07-06 13:02:03,064 - __main__ - INFO - Creating application instance
2025-07-06 13:02:03,079 - __main__ - INFO - Creating main window
2025-07-06 13:02:03,080 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-06 13:02:03,080 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("users")
2025-07-06 13:02:03,080 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 13:02:03,082 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("customers")
2025-07-06 13:02:03,082 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 13:02:03,082 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("product_categories")
2025-07-06 13:02:03,082 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 13:02:03,083 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("products")
2025-07-06 13:02:03,083 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 13:02:03,083 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("invoices")
2025-07-06 13:02:03,083 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 13:02:03,083 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("invoice_lines")
2025-07-06 13:02:03,083 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 13:02:03,083 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-06 13:02:03,151 - __main__ - INFO - Starting application event loop
2025-07-06 13:02:16,824 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-06 13:02:16,827 - sqlalchemy.engine.Engine - INFO - SELECT customers.name AS customers_name, customers.contact_person AS customers_contact_person, customers.phone AS customers_phone, customers.mobile AS customers_mobile, customers.email AS customers_email, customers.address AS customers_address, customers.city AS customers_city, customers.state AS customers_state, customers.postal_code AS customers_postal_code, customers.country AS customers_country, customers.tax_id AS customers_tax_id, customers.credit_limit AS customers_credit_limit, customers.current_balance AS customers_current_balance, customers.notes AS customers_notes, customers.id AS customers_id, customers.created_at AS customers_created_at, customers.updated_at AS customers_updated_at 
FROM customers ORDER BY customers.name
2025-07-06 13:02:16,827 - sqlalchemy.engine.Engine - INFO - [generated in 0.00040s] ()
2025-07-06 13:02:16,828 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-06 13:09:44,714 - __main__ - CRITICAL - Unhandled exception
Traceback (most recent call last):
  File "C:\HamzaZareiProgram\RabieAlKhaleejApp\modules\customer_management.py", line 550, in add_customer
    dialog = CustomerForm(parent=self)
             ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\HamzaZareiProgram\RabieAlKhaleejApp\modules\customer_management.py", line 36, in __init__
    self.setup_ui()
  File "C:\HamzaZareiProgram\RabieAlKhaleejApp\modules\customer_management.py", line 148, in setup_ui
    self.code_edit.setStyleSheet("QLineEdit { border: 2px solid #ff6b6b; }")
    ^^^^^^^^^^^^^^
AttributeError: 'CustomerForm' object has no attribute 'code_edit'
2025-07-06 13:09:45,994 - __main__ - CRITICAL - Unhandled exception
Traceback (most recent call last):
  File "C:\HamzaZareiProgram\RabieAlKhaleejApp\modules\customer_management.py", line 550, in add_customer
    dialog = CustomerForm(parent=self)
             ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\HamzaZareiProgram\RabieAlKhaleejApp\modules\customer_management.py", line 36, in __init__
    self.setup_ui()
  File "C:\HamzaZareiProgram\RabieAlKhaleejApp\modules\customer_management.py", line 148, in setup_ui
    self.code_edit.setStyleSheet("QLineEdit { border: 2px solid #ff6b6b; }")
    ^^^^^^^^^^^^^^
AttributeError: 'CustomerForm' object has no attribute 'code_edit'
2025-07-06 13:09:46,187 - __main__ - CRITICAL - Unhandled exception
Traceback (most recent call last):
  File "C:\HamzaZareiProgram\RabieAlKhaleejApp\modules\customer_management.py", line 550, in add_customer
    dialog = CustomerForm(parent=self)
             ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\HamzaZareiProgram\RabieAlKhaleejApp\modules\customer_management.py", line 36, in __init__
    self.setup_ui()
  File "C:\HamzaZareiProgram\RabieAlKhaleejApp\modules\customer_management.py", line 148, in setup_ui
    self.code_edit.setStyleSheet("QLineEdit { border: 2px solid #ff6b6b; }")
    ^^^^^^^^^^^^^^
AttributeError: 'CustomerForm' object has no attribute 'code_edit'
2025-07-06 13:09:46,357 - __main__ - CRITICAL - Unhandled exception
Traceback (most recent call last):
  File "C:\HamzaZareiProgram\RabieAlKhaleejApp\modules\customer_management.py", line 550, in add_customer
    dialog = CustomerForm(parent=self)
             ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\HamzaZareiProgram\RabieAlKhaleejApp\modules\customer_management.py", line 36, in __init__
    self.setup_ui()
  File "C:\HamzaZareiProgram\RabieAlKhaleejApp\modules\customer_management.py", line 148, in setup_ui
    self.code_edit.setStyleSheet("QLineEdit { border: 2px solid #ff6b6b; }")
    ^^^^^^^^^^^^^^
AttributeError: 'CustomerForm' object has no attribute 'code_edit'
2025-07-06 13:09:51,504 - __main__ - INFO - Starting application initialization
2025-07-06 13:09:51,544 - config.settings - INFO - Application starting in development mode
2025-07-06 13:09:51,544 - config.settings - INFO - Database: sqlite:///./data/rabie_alkhaleej.db
2025-07-06 13:09:51,816 - __main__ - INFO - Initializing database...
2025-07-06 13:09:51,816 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-06 13:09:51,817 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("users")
2025-07-06 13:09:51,817 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 13:09:51,817 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("customers")
2025-07-06 13:09:51,817 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 13:09:51,817 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("product_categories")
2025-07-06 13:09:51,818 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 13:09:51,818 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("products")
2025-07-06 13:09:51,818 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 13:09:51,819 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("invoices")
2025-07-06 13:09:51,819 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 13:09:51,819 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("invoice_lines")
2025-07-06 13:09:51,819 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 13:09:51,819 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-06 13:09:51,821 - __main__ - INFO - Database initialized successfully
2025-07-06 13:09:51,821 - __main__ - INFO - Creating application instance
2025-07-06 13:09:51,844 - __main__ - INFO - Creating main window
2025-07-06 13:09:51,845 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-06 13:09:51,846 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("users")
2025-07-06 13:09:51,846 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 13:09:51,846 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("customers")
2025-07-06 13:09:51,846 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 13:09:51,847 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("product_categories")
2025-07-06 13:09:51,847 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 13:09:51,847 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("products")
2025-07-06 13:09:51,847 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 13:09:51,849 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("invoices")
2025-07-06 13:09:51,849 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 13:09:51,849 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("invoice_lines")
2025-07-06 13:09:51,849 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 13:09:51,850 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-06 13:09:51,937 - __main__ - INFO - Starting application event loop
2025-07-06 13:09:53,256 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-06 13:09:53,258 - sqlalchemy.engine.Engine - INFO - SELECT customers.name AS customers_name, customers.contact_person AS customers_contact_person, customers.phone AS customers_phone, customers.mobile AS customers_mobile, customers.email AS customers_email, customers.address AS customers_address, customers.city AS customers_city, customers.state AS customers_state, customers.postal_code AS customers_postal_code, customers.country AS customers_country, customers.tax_id AS customers_tax_id, customers.credit_limit AS customers_credit_limit, customers.current_balance AS customers_current_balance, customers.notes AS customers_notes, customers.id AS customers_id, customers.created_at AS customers_created_at, customers.updated_at AS customers_updated_at 
FROM customers ORDER BY customers.name
2025-07-06 13:09:53,258 - sqlalchemy.engine.Engine - INFO - [generated in 0.00033s] ()
2025-07-06 13:09:53,260 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-06 13:09:54,404 - __main__ - CRITICAL - Unhandled exception
Traceback (most recent call last):
  File "C:\HamzaZareiProgram\RabieAlKhaleejApp\modules\customer_management.py", line 550, in add_customer
    dialog = CustomerForm(parent=self)
             ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\HamzaZareiProgram\RabieAlKhaleejApp\modules\customer_management.py", line 36, in __init__
    self.setup_ui()
  File "C:\HamzaZareiProgram\RabieAlKhaleejApp\modules\customer_management.py", line 148, in setup_ui
    self.code_edit.setStyleSheet("QLineEdit { border: 2px solid #ff6b6b; }")
    ^^^^^^^^^^^^^^
AttributeError: 'CustomerForm' object has no attribute 'code_edit'
2025-07-06 13:09:54,689 - __main__ - CRITICAL - Unhandled exception
Traceback (most recent call last):
  File "C:\HamzaZareiProgram\RabieAlKhaleejApp\modules\customer_management.py", line 550, in add_customer
    dialog = CustomerForm(parent=self)
             ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\HamzaZareiProgram\RabieAlKhaleejApp\modules\customer_management.py", line 36, in __init__
    self.setup_ui()
  File "C:\HamzaZareiProgram\RabieAlKhaleejApp\modules\customer_management.py", line 148, in setup_ui
    self.code_edit.setStyleSheet("QLineEdit { border: 2px solid #ff6b6b; }")
    ^^^^^^^^^^^^^^
AttributeError: 'CustomerForm' object has no attribute 'code_edit'
2025-07-06 13:09:58,774 - __main__ - INFO - Starting application initialization
2025-07-06 13:09:58,813 - config.settings - INFO - Application starting in development mode
2025-07-06 13:09:58,814 - config.settings - INFO - Database: sqlite:///./data/rabie_alkhaleej.db
2025-07-06 13:09:59,060 - __main__ - INFO - Initializing database...
2025-07-06 13:09:59,060 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-06 13:09:59,061 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("users")
2025-07-06 13:09:59,061 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 13:09:59,061 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("customers")
2025-07-06 13:09:59,061 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 13:09:59,063 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("product_categories")
2025-07-06 13:09:59,063 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 13:09:59,063 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("products")
2025-07-06 13:09:59,063 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 13:09:59,063 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("invoices")
2025-07-06 13:09:59,064 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 13:09:59,064 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("invoice_lines")
2025-07-06 13:09:59,064 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 13:09:59,065 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-06 13:09:59,065 - __main__ - INFO - Database initialized successfully
2025-07-06 13:09:59,065 - __main__ - INFO - Creating application instance
2025-07-06 13:09:59,083 - __main__ - INFO - Creating main window
2025-07-06 13:09:59,083 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-06 13:09:59,084 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("users")
2025-07-06 13:09:59,084 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 13:09:59,084 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("customers")
2025-07-06 13:09:59,084 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 13:09:59,084 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("product_categories")
2025-07-06 13:09:59,085 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 13:09:59,085 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("products")
2025-07-06 13:09:59,085 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 13:09:59,085 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("invoices")
2025-07-06 13:09:59,085 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 13:09:59,086 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("invoice_lines")
2025-07-06 13:09:59,086 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 13:09:59,086 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-06 13:09:59,161 - __main__ - INFO - Starting application event loop
2025-07-06 13:10:04,920 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-06 13:10:04,922 - sqlalchemy.engine.Engine - INFO - SELECT customers.name AS customers_name, customers.contact_person AS customers_contact_person, customers.phone AS customers_phone, customers.mobile AS customers_mobile, customers.email AS customers_email, customers.address AS customers_address, customers.city AS customers_city, customers.state AS customers_state, customers.postal_code AS customers_postal_code, customers.country AS customers_country, customers.tax_id AS customers_tax_id, customers.credit_limit AS customers_credit_limit, customers.current_balance AS customers_current_balance, customers.notes AS customers_notes, customers.id AS customers_id, customers.created_at AS customers_created_at, customers.updated_at AS customers_updated_at 
FROM customers ORDER BY customers.name
2025-07-06 13:10:04,922 - sqlalchemy.engine.Engine - INFO - [generated in 0.00038s] ()
2025-07-06 13:10:04,922 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-06 13:10:35,638 - __main__ - CRITICAL - Unhandled exception
Traceback (most recent call last):
  File "C:\HamzaZareiProgram\RabieAlKhaleejApp\modules\customer_management.py", line 550, in add_customer
    dialog = CustomerForm(parent=self)
             ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\HamzaZareiProgram\RabieAlKhaleejApp\modules\customer_management.py", line 36, in __init__
    self.setup_ui()
  File "C:\HamzaZareiProgram\RabieAlKhaleejApp\modules\customer_management.py", line 148, in setup_ui
    self.code_edit.setStyleSheet("QLineEdit { border: 2px solid #ff6b6b; }")
    ^^^^^^^^^^^^^^
AttributeError: 'CustomerForm' object has no attribute 'code_edit'
2025-07-06 13:10:37,497 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-06 13:10:37,497 - sqlalchemy.engine.Engine - INFO - SELECT customers.name AS customers_name, customers.contact_person AS customers_contact_person, customers.phone AS customers_phone, customers.mobile AS customers_mobile, customers.email AS customers_email, customers.address AS customers_address, customers.city AS customers_city, customers.state AS customers_state, customers.postal_code AS customers_postal_code, customers.country AS customers_country, customers.tax_id AS customers_tax_id, customers.credit_limit AS customers_credit_limit, customers.current_balance AS customers_current_balance, customers.notes AS customers_notes, customers.id AS customers_id, customers.created_at AS customers_created_at, customers.updated_at AS customers_updated_at 
FROM customers ORDER BY customers.name
2025-07-06 13:10:37,497 - sqlalchemy.engine.Engine - INFO - [cached since 32.58s ago] ()
2025-07-06 13:10:37,498 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-06 13:10:38,127 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-06 13:10:38,127 - sqlalchemy.engine.Engine - INFO - SELECT customers.name AS customers_name, customers.contact_person AS customers_contact_person, customers.phone AS customers_phone, customers.mobile AS customers_mobile, customers.email AS customers_email, customers.address AS customers_address, customers.city AS customers_city, customers.state AS customers_state, customers.postal_code AS customers_postal_code, customers.country AS customers_country, customers.tax_id AS customers_tax_id, customers.credit_limit AS customers_credit_limit, customers.current_balance AS customers_current_balance, customers.notes AS customers_notes, customers.id AS customers_id, customers.created_at AS customers_created_at, customers.updated_at AS customers_updated_at 
FROM customers ORDER BY customers.name
2025-07-06 13:10:38,127 - sqlalchemy.engine.Engine - INFO - [cached since 33.2s ago] ()
2025-07-06 13:10:38,128 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-06 13:10:38,518 - __main__ - CRITICAL - Unhandled exception
Traceback (most recent call last):
  File "C:\HamzaZareiProgram\RabieAlKhaleejApp\modules\customer_management.py", line 550, in add_customer
    dialog = CustomerForm(parent=self)
             ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\HamzaZareiProgram\RabieAlKhaleejApp\modules\customer_management.py", line 36, in __init__
    self.setup_ui()
  File "C:\HamzaZareiProgram\RabieAlKhaleejApp\modules\customer_management.py", line 148, in setup_ui
    self.code_edit.setStyleSheet("QLineEdit { border: 2px solid #ff6b6b; }")
    ^^^^^^^^^^^^^^
AttributeError: 'CustomerForm' object has no attribute 'code_edit'
2025-07-06 13:12:55,604 - __main__ - INFO - Starting application initialization
2025-07-06 13:12:55,643 - config.settings - INFO - Application starting in development mode
2025-07-06 13:12:55,643 - config.settings - INFO - Database: sqlite:///./data/rabie_alkhaleej.db
2025-07-06 13:12:55,893 - __main__ - INFO - Initializing database...
2025-07-06 13:12:55,894 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-06 13:12:55,894 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("users")
2025-07-06 13:12:55,895 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 13:12:55,895 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("customers")
2025-07-06 13:12:55,895 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 13:12:55,897 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("product_categories")
2025-07-06 13:12:55,897 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 13:12:55,897 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("products")
2025-07-06 13:12:55,897 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 13:12:55,897 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("invoices")
2025-07-06 13:12:55,898 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 13:12:55,898 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("invoice_lines")
2025-07-06 13:12:55,899 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 13:12:55,899 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-06 13:12:55,899 - __main__ - INFO - Database initialized successfully
2025-07-06 13:12:55,899 - __main__ - INFO - Creating application instance
2025-07-06 13:12:55,916 - __main__ - INFO - Creating main window
2025-07-06 13:12:55,917 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-06 13:12:55,917 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("users")
2025-07-06 13:12:55,918 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 13:12:55,918 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("customers")
2025-07-06 13:12:55,918 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 13:12:55,918 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("product_categories")
2025-07-06 13:12:55,919 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 13:12:55,919 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("products")
2025-07-06 13:12:55,919 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 13:12:55,920 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("invoices")
2025-07-06 13:12:55,920 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 13:12:55,920 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("invoice_lines")
2025-07-06 13:12:55,920 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 13:12:55,920 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-06 13:12:56,006 - __main__ - INFO - Starting application event loop
2025-07-06 13:12:57,342 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-06 13:12:57,344 - sqlalchemy.engine.Engine - INFO - SELECT customers.name AS customers_name, customers.contact_person AS customers_contact_person, customers.phone AS customers_phone, customers.mobile AS customers_mobile, customers.email AS customers_email, customers.address AS customers_address, customers.city AS customers_city, customers.state AS customers_state, customers.postal_code AS customers_postal_code, customers.country AS customers_country, customers.tax_id AS customers_tax_id, customers.credit_limit AS customers_credit_limit, customers.current_balance AS customers_current_balance, customers.notes AS customers_notes, customers.id AS customers_id, customers.created_at AS customers_created_at, customers.updated_at AS customers_updated_at 
FROM customers ORDER BY customers.name
2025-07-06 13:12:57,344 - sqlalchemy.engine.Engine - INFO - [generated in 0.00046s] ()
2025-07-06 13:12:57,345 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-06 13:13:18,476 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-06 13:13:18,476 - sqlalchemy.engine.Engine - INFO - SELECT customers.name AS customers_name, customers.contact_person AS customers_contact_person, customers.phone AS customers_phone, customers.mobile AS customers_mobile, customers.email AS customers_email, customers.address AS customers_address, customers.city AS customers_city, customers.state AS customers_state, customers.postal_code AS customers_postal_code, customers.country AS customers_country, customers.tax_id AS customers_tax_id, customers.credit_limit AS customers_credit_limit, customers.current_balance AS customers_current_balance, customers.notes AS customers_notes, customers.id AS customers_id, customers.created_at AS customers_created_at, customers.updated_at AS customers_updated_at 
FROM customers ORDER BY customers.name
2025-07-06 13:13:18,477 - sqlalchemy.engine.Engine - INFO - [cached since 21.13s ago] ()
2025-07-06 13:13:18,478 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-06 13:14:55,760 - __main__ - INFO - Starting application initialization
2025-07-06 13:14:55,798 - config.settings - INFO - Application starting in development mode
2025-07-06 13:14:55,798 - config.settings - INFO - Database: sqlite:///./data/rabie_alkhaleej.db
2025-07-06 13:14:56,051 - __main__ - INFO - Initializing database...
2025-07-06 13:14:56,052 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-06 13:14:56,052 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("users")
2025-07-06 13:14:56,052 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 13:14:56,054 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("customers")
2025-07-06 13:14:56,054 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 13:14:56,054 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("product_categories")
2025-07-06 13:14:56,055 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 13:14:56,055 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("products")
2025-07-06 13:14:56,055 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 13:14:56,055 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("invoices")
2025-07-06 13:14:56,056 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 13:14:56,056 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("invoice_lines")
2025-07-06 13:14:56,056 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 13:14:56,057 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-06 13:14:56,057 - __main__ - INFO - Database initialized successfully
2025-07-06 13:14:56,057 - __main__ - INFO - Creating application instance
2025-07-06 13:14:56,075 - __main__ - INFO - Creating main window
2025-07-06 13:14:56,076 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-06 13:14:56,076 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("users")
2025-07-06 13:14:56,076 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 13:14:56,077 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("customers")
2025-07-06 13:14:56,077 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 13:14:56,078 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("product_categories")
2025-07-06 13:14:56,078 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 13:14:56,078 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("products")
2025-07-06 13:14:56,078 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 13:14:56,078 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("invoices")
2025-07-06 13:14:56,078 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 13:14:56,079 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("invoice_lines")
2025-07-06 13:14:56,079 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-06 13:14:56,079 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-06 13:14:56,176 - __main__ - INFO - Starting application event loop
2025-07-06 13:14:57,178 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-06 13:14:57,181 - sqlalchemy.engine.Engine - INFO - SELECT customers.name AS customers_name, customers.contact_person AS customers_contact_person, customers.phone AS customers_phone, customers.mobile AS customers_mobile, customers.email AS customers_email, customers.address AS customers_address, customers.city AS customers_city, customers.state AS customers_state, customers.postal_code AS customers_postal_code, customers.country AS customers_country, customers.tax_id AS customers_tax_id, customers.credit_limit AS customers_credit_limit, customers.current_balance AS customers_current_balance, customers.notes AS customers_notes, customers.id AS customers_id, customers.created_at AS customers_created_at, customers.updated_at AS customers_updated_at 
FROM customers ORDER BY customers.name
2025-07-06 13:14:57,182 - sqlalchemy.engine.Engine - INFO - [generated in 0.00075s] ()
2025-07-06 13:14:57,183 - sqlalchemy.engine.Engine - INFO - ROLLBACK
