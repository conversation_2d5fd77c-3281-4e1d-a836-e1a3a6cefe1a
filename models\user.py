from sqlalchemy import Colum<PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, Integer, DateTime
from datetime import datetime
from .base import BaseModel

class User(BaseModel):
    """User model for authentication and authorization."""
    __tablename__ = "users"
    
    username = Column(String(50), unique=True, index=True, nullable=False)
    email = Column(String(100), unique=True, index=True, nullable=True)
    hashed_password = Column(String(100), nullable=False)
    full_name = Column(String(100), nullable=True)
    is_active = Column(Boolean, default=True)
    is_admin = Column(Boolean, default=False)
    last_login = Column(DateTime, nullable=True)
    
    # Additional user-related fields can be added here
    phone = Column(String(20), nullable=True)
    address = Column(String(200), nullable=True)
    
    def __repr__(self):
        return f"<User {self.username}>"
    
    @property
    def is_authenticated(self):
        return self.is_active
    
    @property
    def display_name(self):
        return self.full_name or self.username
