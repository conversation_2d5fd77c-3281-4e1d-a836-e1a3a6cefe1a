from PyQt6.QtWidgets import (QMainWindow, Q<PERSON><PERSON><PERSON>, QWidget, QVBoxLayout, QHBoxLayout,
                             QPushButton, QTabWidget, QLabel, QStatusBar, QToolBar, QMessageBox,
                             QDockWidget, QListWidget, QStackedWidget, QMenuBar, QMenu, QFileDialog)
from PyQt6.QtCore import Qt, QSize
from PyQt6.QtGui import QIcon, QAction, QPixmap
import sys
import os

from config import settings
from database.session import init_db
from modules.customer_management import CustomerManagementWidget

class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle(f"{settings.APP_NAME} - {settings.VERSION}")
        self.setMinimumSize(settings.UI_SETTINGS['min_width'], settings.UI_SETTINGS['min_height'])
        
        # Initialize database
        if not init_db():
            QMessageBox.critical(self, "Database Error", "Failed to connect to the database.")
            sys.exit(1)
        
        # Set up the main window
        self.setup_ui()
        
    def setup_ui(self):
        """Set up the main user interface."""
        # Create central widget and layout
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        main_layout = QVBoxLayout(central_widget)
        
        # Create menu bar
        self.create_menu_bar()
        
        # Create toolbar
        self.create_toolbar()
        
        # Create main content area
        content_layout = QHBoxLayout()
        
        # Create navigation panel (left sidebar)
        self.nav_panel = self.create_navigation_panel()
        content_layout.addWidget(self.nav_panel, stretch=1)
        
        # Create main content area (right side)
        self.main_content = QStackedWidget()
        content_layout.addWidget(self.main_content, stretch=4)
        
        # Add content layout to main layout
        main_layout.addLayout(content_layout)

        # Create status bar
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        self.status_bar.showMessage("Ready")

        # Apply styles
        self.apply_styles()

        # Show default module (Dashboard)
        self.show_module("Dashboard")
    
    def create_menu_bar(self):
        """Create the menu bar."""
        menubar = self.menuBar()
        
        # File menu
        file_menu = menubar.addMenu("&File")
        
        # File actions
        new_action = QAction("&New", self)
        new_action.setShortcut("Ctrl+N")
        new_action.triggered.connect(self.new_document)
        
        open_action = QAction("&Open", self)
        open_action.setShortcut("Ctrl+O")
        open_action.triggered.connect(self.open_document)
        
        save_action = QAction("&Save", self)
        save_action.setShortcut("Ctrl+S")
        save_action.triggered.connect(self.save_document)
        
        exit_action = QAction("E&xit", self)
        exit_action.setShortcut("Alt+F4")
        exit_action.triggered.connect(self.close)
        
        file_menu.addAction(new_action)
        file_menu.addAction(open_action)
        file_menu.addAction(save_action)
        file_menu.addSeparator()
        file_menu.addAction(exit_action)
        
        # Edit menu
        edit_menu = menubar.addMenu("&Edit")
        
        # View menu
        view_menu = menubar.addMenu("&View")
        
        # Tools menu
        tools_menu = menubar.addMenu("&Tools")
        
        # Help menu
        help_menu = menubar.addMenu("&Help")
        about_action = QAction("&About", self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)
    
    def create_toolbar(self):
        """Create the main toolbar."""
        toolbar = QToolBar("Main Toolbar")
        toolbar.setIconSize(QSize(24, 24))
        self.addToolBar(toolbar)
        
        # Add actions to toolbar
        new_action = QAction("New", self)
        new_action.setStatusTip("Create a new document")
        toolbar.addAction(new_action)
        
        save_action = QAction("Save", self)
        save_action.setStatusTip("Save the current document")
        toolbar.addAction(save_action)
        
        toolbar.addSeparator()
        
        cut_action = QAction("Cut", self)
        cut_action.setStatusTip("Cut selection to clipboard")
        toolbar.addAction(cut_action)
        
        copy_action = QAction("Copy", self)
        copy_action.setStatusTip("Copy selection to clipboard")
        toolbar.addAction(copy_action)
        
        paste_action = QAction("Paste", self)
        paste_action.setStatusTip("Paste from clipboard")
        toolbar.addAction(paste_action)
    
    def create_navigation_panel(self):
        """Create the navigation panel."""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        
        # Add navigation buttons
        modules = [
            "Dashboard", "Customers", "Sales", "Purchases", "Inventory",
            "Accounting", "Banking", "Reports", "Settings"
        ]
        
        for module in modules:
            btn = QPushButton(module)
            btn.setCheckable(True)
            btn.clicked.connect(lambda checked, m=module: self.show_module(m))
            layout.addWidget(btn)
        
        layout.addStretch()
        return panel
    
    def show_module(self, module_name):
        """Show the selected module in the main content area."""
        # Clear current content
        while self.main_content.count() > 0:
            widget = self.main_content.widget(0)
            self.main_content.removeWidget(widget)
            widget.setParent(None)

        # Create and add the appropriate module widget
        if module_name == "Dashboard":
            # TODO: Implement dashboard
            widget = QWidget()
            layout = QVBoxLayout(widget)
            layout.addWidget(QLabel("Dashboard - Coming Soon"))
            self.main_content.addWidget(widget)

        elif module_name == "Customers":
            # Customer Management Module
            widget = CustomerManagementWidget()
            self.main_content.addWidget(widget)

        elif module_name == "Sales":
            # TODO: Implement sales module
            widget = QWidget()
            layout = QVBoxLayout(widget)
            layout.addWidget(QLabel("Sales Module - Coming Soon"))
            self.main_content.addWidget(widget)

        elif module_name == "Purchases":
            # TODO: Implement purchases module
            widget = QWidget()
            layout = QVBoxLayout(widget)
            layout.addWidget(QLabel("Purchases Module - Coming Soon"))
            self.main_content.addWidget(widget)

        elif module_name == "Inventory":
            # TODO: Implement inventory module
            widget = QWidget()
            layout = QVBoxLayout(widget)
            layout.addWidget(QLabel("Inventory Module - Coming Soon"))
            self.main_content.addWidget(widget)

        elif module_name == "Accounting":
            # TODO: Implement accounting module
            widget = QWidget()
            layout = QVBoxLayout(widget)
            layout.addWidget(QLabel("Accounting Module - Coming Soon"))
            self.main_content.addWidget(widget)

        elif module_name == "Banking":
            # TODO: Implement banking module
            widget = QWidget()
            layout = QVBoxLayout(widget)
            layout.addWidget(QLabel("Banking Module - Coming Soon"))
            self.main_content.addWidget(widget)

        elif module_name == "Reports":
            # TODO: Implement reports module
            widget = QWidget()
            layout = QVBoxLayout(widget)
            layout.addWidget(QLabel("Reports Module - Coming Soon"))
            self.main_content.addWidget(widget)

        elif module_name == "Settings":
            # TODO: Implement settings module
            widget = QWidget()
            layout = QVBoxLayout(widget)
            layout.addWidget(QLabel("Settings Module - Coming Soon"))
            self.main_content.addWidget(widget)

        else:
            # Default case - show placeholder
            widget = QWidget()
            layout = QVBoxLayout(widget)
            layout.addWidget(QLabel(f"{module_name} Module - Coming Soon"))
            self.main_content.addWidget(widget)

        # Update navigation button states
        for i in range(self.nav_panel.layout().count() - 1):  # -1 for stretch
            item = self.nav_panel.layout().itemAt(i)
            if item and item.widget():
                button = item.widget()
                if isinstance(button, QPushButton):
                    button.setChecked(button.text() == module_name)

        self.status_bar.showMessage(f"Showing {module_name} module")
    
    def new_document(self):
        """Handle new document action."""
        # TODO: Implement new document
        self.status_bar.showMessage("New document created")
    
    def open_document(self):
        """Handle open document action."""
        file_name, _ = QFileDialog.getOpenFileName(
            self, "Open Document", "", "All Files (*);;Database Files (*.mdb *.accdb)")
        if file_name:
            # TODO: Handle file opening
            self.status_bar.showMessage(f"Opened: {file_name}")
    
    def save_document(self):
        """Handle save document action."""
        # TODO: Implement save functionality
        self.status_bar.showMessage("Document saved")
    
    def show_about(self):
        """Show about dialog."""
        QMessageBox.about(
            self,
            f"About {settings.APP_NAME}",
            f"""
            <h2>{settings.APP_NAME}</h2>
            <p>Version: {settings.VERSION}</p>
            <p>A comprehensive accounting and business management solution.</p>
            <p>© 2025 Rabie Al-Khaleej. All rights reserved.</p>
            """
        )
    
    def apply_styles(self):
        """Apply styles to the application."""
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f0f0f0;
            }
            QPushButton {
                padding: 8px;
                margin: 2px;
                border: 1px solid #ccc;
                border-radius: 4px;
                background-color: #f8f8f8;
            }
            QPushButton:hover {
                background-color: #e0e0e0;
            }
            QPushButton:checked {
                background-color: #d0d0d0;
                font-weight: bold;
            }
            QStatusBar {
                background-color: #e0e0e0;
                padding: 2px;
            }
        """)
    
    def closeEvent(self, event):
        """Handle window close event."""
        reply = QMessageBox.question(
            self, 'Exit',
            'Are you sure you want to exit?',
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            # Clean up resources
            # Note: SQLite connections are automatically closed
            event.accept()
        else:
            event.ignore()

def main():
    """Main entry point for the application."""
    app = QApplication(sys.argv)
    
    # Set application style
    app.setStyle(settings.UI_SETTINGS['theme'])
    
    # Create and show the main window
    window = MainWindow()
    window.show()
    
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
