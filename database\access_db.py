"""
Module for interacting with Microsoft Access database.
"""
import pyodbc
from pathlib import Path
from typing import List, Dict, Any, Optional

class AccessDatabase:
    """Class to handle Microsoft Access database operations."""
    
    def __init__(self, db_path: str):
        """Initialize with path to Access database file.
        
        Args:
            db_path: Path to the .mdb or .accdb file
        """
        self.db_path = Path(db_path).resolve()
        self.conn = None
        self.cursor = None
    
    def connect(self, username: str = '', password: str = '') -> bool:
        """Connect to the Access database.
        
        Args:
            username: Database username (if required)
            password: Database password (if required)
            
        Returns:
            bool: True if connection was successful, False otherwise
        """
        if not self.db_path.exists():
            print(f"Error: Database file not found at {self.db_path}")
            return False
            
        try:
            # Try different connection strings
            conn_strs = [
                # Standard connection string
                f'DRIVER={{Microsoft Access Driver (*.mdb, *.accdb)}};DBQ={self.db_path};',
                # With user credentials
                f'DRIVER={{Microsoft Access Driver (*.mdb, *.accdb)}};DBQ={self.db_path};UID={username};PWD={password};',
                # Alternative driver name
                f'DRIVER={{Microsoft Access Driver (*.mdb)}};DBQ={self.db_path};',
                f'DRIVER={{Microsoft Access Driver (*.mdb)}};DBQ={self.db_path};UID={username};PWD={password};',
            ]
            
            for conn_str in conn_strs:
                try:
                    self.conn = pyodbc.connect(conn_str)
                    self.cursor = self.conn.cursor()
                    print(f"Successfully connected to {self.db_path.name}")
                    return True
                except pyodbc.Error as e:
                    last_error = str(e)
                    continue
            
            print(f"Failed to connect to database: {last_error}")
            return False
            
        except Exception as e:
            print(f"Error connecting to database: {e}")
            return False
    
    def disconnect(self):
        """Close the database connection."""
        if self.cursor:
            self.cursor.close()
        if self.conn:
            self.conn.close()
        print("Database connection closed.")
    
    def get_tables(self) -> List[str]:
        """Get list of all tables in the database.
        
        Returns:
            List of table names
        """
        if not self.cursor:
            print("Not connected to database")
            return []
            
        try:
            tables = [row.table_name for row in self.cursor.tables(tableType='TABLE') 
                     if not row.table_name.startswith('MSys')]
            return sorted(tables)
        except Exception as e:
            print(f"Error getting tables: {e}")
            return []
    
    def get_table_columns(self, table_name: str) -> List[Dict[str, Any]]:
        """Get column information for a table.
        
        Args:
            table_name: Name of the table
            
        Returns:
            List of dictionaries with column information
        """
        if not self.cursor:
            print("Not connected to database")
            return []
            
        try:
            columns = []
            for row in self.cursor.columns(table=table_name):
                columns.append({
                    'name': row.column_name,
                    'type': row.type_name,
                    'size': row.column_size,
                    'nullable': row.nullable == 1,
                    'primary_key': False  # Will be updated later
                })
            
            # Try to get primary key information
            try:
                pkeys = [row.column_name for row in self.cursor.primaryKeys(table=table_name)]
                for col in columns:
                    if col['name'] in pkeys:
                        col['primary_key'] = True
            except:
                pass  # Some drivers don't support primaryKeys()
                
            return columns
            
        except Exception as e:
            print(f"Error getting columns for table {table_name}: {e}")
            return []
    
    def get_table_data(self, table_name: str, limit: int = 10) -> List[Dict[str, Any]]:
        """Get data from a table.
        
        Args:
            table_name: Name of the table
            limit: Maximum number of rows to return
            
        Returns:
            List of dictionaries containing row data
        """
        if not self.cursor:
            print("Not connected to database")
            return []
            
        try:
            # First get column names
            columns = [col['name'] for col in self.get_table_columns(table_name)]
            
            # Query data
            query = f"SELECT TOP {limit} * FROM [{table_name}]"
            self.cursor.execute(query)
            
            # Convert rows to dictionaries
            rows = []
            for row in self.cursor.fetchall():
                rows.append(dict(zip(columns, row)))
                
            return rows
            
        except Exception as e:
            print(f"Error getting data from table {table_name}: {e}")
            return []
    
    def get_relationships(self) -> List[Dict[str, Any]]:
        """Get relationships between tables.
        
        Returns:
            List of relationship information
        """
        if not self.cursor:
            print("Not connected to database")
            return []
            
        try:
            # This is a best-effort approach as Access doesn't always expose relationships well
            relationships = []
            
            # Try to get relationships from system tables
            try:
                self.cursor.execute("""
                    SELECT szObject, szColumn, szReferencedObject, szReferencedColumn, szRelationship
                    FROM MSysRelationships
                """)
                
                for row in self.cursor.fetchall():
                    relationships.append({
                        'table': row.szObject,
                        'column': row.szColumn,
                        'referenced_table': row.szReferencedObject,
                        'referenced_column': row.szReferencedColumn,
                        'name': row.szRelationship
                    })
            except:
                print("Could not retrieve relationships from MSysRelationships")
            
            return relationships
            
        except Exception as e:
            print(f"Error getting relationships: {e}")
            return []


def analyze_access_database(db_path: str, username: str = '', password: str = '') -> Dict[str, Any]:
    """Analyze an Access database and return its structure.
    
    Args:
        db_path: Path to the Access database file
        username: Database username (if required)
        password: Database password (if required)
        
    Returns:
        Dictionary containing database structure information
    """
    db = AccessDatabase(db_path)
    
    if not db.connect(username, password):
        return {"error": "Failed to connect to database"}
    
    try:
        # Get database structure
        tables = db.get_tables()
        
        db_structure = {
            'tables': {},
            'relationships': db.get_relationships()
        }
        
        # Get structure for each table
        for table in tables:
            columns = db.get_table_columns(table)
            sample_data = db.get_table_data(table, limit=3)
            
            db_structure['tables'][table] = {
                'columns': columns,
                'sample_data': sample_data,
                'row_count': len(sample_data)  # This is just sample count, not total
            }
        
        return db_structure
        
    finally:
        db.disconnect()


if __name__ == "__main__":
    # Example usage
    db_path = r"c:\HamzaZareiProgram\RabieAl-KhaleejMulti.mdb"
    username = "majid"
    password = "majid"
    
    print(f"Analyzing database: {db_path}")
    structure = analyze_access_database(db_path, username, password)
    
    # Print summary
    print("\n=== Database Structure ===")
    print(f"Found {len(structure['tables'])} tables:")
    
    for table_name, table_info in structure['tables'].items():
        print(f"\nTable: {table_name}")
        print(f"Columns: {', '.join(col['name'] for col in table_info['columns'])}")
        
        # Print primary keys
        pks = [col['name'] for col in table_info['columns'] if col.get('primary_key')]
        if pks:
            print(f"Primary Keys: {', '.join(pks)}")
        
        # Print sample data
        if table_info['sample_data']:
            print("Sample data:")
            for row in table_info['sample_data']:
                print(f"  {row}")
    
    # Print relationships
    if structure['relationships']:
        print("\n=== Relationships ===")
        for rel in structure['relationships']:
            print(f"{rel['table']}.{rel['column']} -> {rel['referenced_table']}.{rel['referenced_column']}")
