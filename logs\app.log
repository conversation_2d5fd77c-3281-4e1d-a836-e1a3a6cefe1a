2025-07-04 21:46:45,107 - config.settings - INFO - Application starting in development mode
2025-07-04 21:46:45,107 - config.settings - INFO - Database: sqlite://********@localhost/RabieAlKhaleej
2025-07-04 21:48:58,583 - config.settings - INFO - Application starting in development mode
2025-07-04 21:48:58,583 - config.settings - INFO - Database: sqlite://********@localhost/RabieAlKhaleej
2025-07-04 21:49:35,929 - config.settings - INFO - Application starting in development mode
2025-07-04 21:49:35,930 - config.settings - INFO - Database: sqlite://********@localhost/RabieAlKhaleej
2025-07-04 21:50:08,343 - config.settings - INFO - Application starting in development mode
2025-07-04 21:50:08,343 - config.settings - INFO - Database: sqlite://********@localhost/RabieAlKhaleej
2025-07-04 21:51:54,796 - config.settings - INFO - Application starting in development mode
2025-07-04 21:51:54,796 - config.settings - INFO - Database: sqlite://********@localhost/rabie_alkhaleej.db
2025-07-04 21:53:42,465 - config.settings - INFO - Application starting in development mode
2025-07-04 21:53:42,465 - config.settings - INFO - Database: sqlite://********@localhost/rabie_alkhaleej.db
2025-07-04 21:55:10,181 - config.settings - INFO - Application starting in development mode
2025-07-04 21:55:10,181 - config.settings - INFO - Database: sqlite://********@localhost/rabie_alkhaleej.db
2025-07-04 21:56:16,207 - config.settings - INFO - Application starting in development mode
2025-07-04 21:56:42,082 - config.settings - INFO - Application starting in development mode
2025-07-04 21:57:42,867 - config.settings - INFO - Application starting in development mode
2025-07-04 21:58:09,130 - config.settings - INFO - Application starting in development mode
2025-07-06 12:32:24,652 - config.settings - INFO - Application starting in development mode
2025-07-06 12:32:24,652 - config.settings - INFO - Database: sqlite:///./data/rabie_alkhaleej.db
2025-07-06 12:32:24,671 - __main__ - ERROR - Error adding sample customers: When initializing mapper Mapper[Customer(customers)], expression 'Invoice' failed to locate a name ('Invoice'). If this is a class name, consider adding this relationship() to the <class 'models.customer.Customer'> class after both dependent classes have been defined.
2025-07-06 12:33:19,680 - config.settings - INFO - Application starting in development mode
2025-07-06 12:33:19,680 - config.settings - INFO - Database: sqlite:///./data/rabie_alkhaleej.db
2025-07-06 12:33:19,725 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-06 12:33:19,731 - sqlalchemy.engine.Engine - INFO - SELECT count(*) AS count_1 
FROM (SELECT customers.name AS customers_name, customers.code AS customers_code, customers.contact_person AS customers_contact_person, customers.phone AS customers_phone, customers.mobile AS customers_mobile, customers.email AS customers_email, customers.address AS customers_address, customers.city AS customers_city, customers.state AS customers_state, customers.postal_code AS customers_postal_code, customers.country AS customers_country, customers.tax_id AS customers_tax_id, customers.credit_limit AS customers_credit_limit, customers.current_balance AS customers_current_balance, customers.notes AS customers_notes, customers.is_active AS customers_is_active, customers.id AS customers_id, customers.created_at AS customers_created_at, customers.updated_at AS customers_updated_at 
FROM customers) AS anon_1
2025-07-06 12:33:19,732 - sqlalchemy.engine.Engine - INFO - [generated in 0.00039s] ()
2025-07-06 12:33:19,735 - sqlalchemy.engine.Engine - INFO - INSERT INTO customers (name, code, contact_person, phone, mobile, email, address, city, state, postal_code, country, tax_id, credit_limit, current_balance, notes, is_active, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-06 12:33:19,736 - sqlalchemy.engine.Engine - INFO - [generated in 0.00034s (insertmanyvalues) 1/10 (ordered; batch not supported)] ('ABC Trading Company', 'CUST001', 'Ahmed Al-Rashid', '+971-4-123-4567', '+971-50-123-4567', '<EMAIL>', 'Office 123, Business Bay Tower', 'Dubai', 'Dubai', '12345', 'UAE', 'TRN123456789', 50000.0, 15000.0, 'Long-term customer with excellent payment history', 1, '2025-07-06 09:03:19.735455', '2025-07-06 09:03:19.735455')
2025-07-06 12:33:19,737 - sqlalchemy.engine.Engine - INFO - INSERT INTO customers (name, code, contact_person, phone, mobile, email, address, city, state, postal_code, country, tax_id, credit_limit, current_balance, notes, is_active, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-06 12:33:19,737 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 2/10 (ordered; batch not supported)] ('Gulf Electronics LLC', 'CUST002', 'Fatima Al-Zahra', '+971-2-987-6543', '+971-55-987-6543', '<EMAIL>', 'Industrial Area 2, Warehouse 45', 'Abu Dhabi', 'Abu Dhabi', '54321', 'UAE', 'TRN987654321', 75000.0, 0.0, 'Electronics retailer, bulk orders', 1, '2025-07-06 09:03:19.735455', '2025-07-06 09:03:19.735455')
2025-07-06 12:33:19,738 - sqlalchemy.engine.Engine - INFO - INSERT INTO customers (name, code, contact_person, phone, mobile, email, address, city, state, postal_code, country, tax_id, credit_limit, current_balance, notes, is_active, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-06 12:33:19,738 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 3/10 (ordered; batch not supported)] ('Sharjah Construction Co.', 'CUST003', 'Mohammed Hassan', '+971-6-555-1234', '+971-52-555-1234', '<EMAIL>', 'Industrial Area 1, Plot 67', 'Sharjah', 'Sharjah', '67890', 'UAE', 'TRN555123456', 100000.0, 25000.0, 'Construction materials supplier', 1, '2025-07-06 09:03:19.735455', '2025-07-06 09:03:19.735455')
2025-07-06 12:33:19,738 - sqlalchemy.engine.Engine - INFO - INSERT INTO customers (name, code, contact_person, phone, mobile, email, address, city, state, postal_code, country, tax_id, credit_limit, current_balance, notes, is_active, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-06 12:33:19,738 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 4/10 (ordered; batch not supported)] ('Al-Noor Textiles', 'CUST004', 'Aisha Al-Mansouri', '+971-4-777-8888', '+971-56-777-8888', '<EMAIL>', 'Textile Souk, Shop 234', 'Dubai', 'Dubai', '11111', 'UAE', 'TRN777888999', 30000.0, 5000.0, 'Textile and fabric supplier', 1, '2025-07-06 09:03:19.735455', '2025-07-06 09:03:19.735455')
2025-07-06 12:33:19,739 - sqlalchemy.engine.Engine - INFO - INSERT INTO customers (name, code, contact_person, phone, mobile, email, address, city, state, postal_code, country, tax_id, credit_limit, current_balance, notes, is_active, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-06 12:33:19,739 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 5/10 (ordered; batch not supported)] ('Ras Al Khaimah Motors', 'CUST005', 'Omar Al-Qasimi', '+971-7-222-3333', '+971-50-222-3333', '<EMAIL>', 'Sheikh Mohammed Bin Rashid Road', 'Ras Al Khaimah', 'Ras Al Khaimah', '22222', 'UAE', 'TRN222333444', 80000.0, 0.0, 'Automotive parts and accessories', 1, '2025-07-06 09:03:19.735455', '2025-07-06 09:03:19.735455')
2025-07-06 12:33:19,739 - sqlalchemy.engine.Engine - INFO - INSERT INTO customers (name, code, contact_person, phone, mobile, email, address, city, state, postal_code, country, tax_id, credit_limit, current_balance, notes, is_active, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-06 12:33:19,740 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 6/10 (ordered; batch not supported)] ('Fujairah Food Supplies', 'CUST006', 'Mariam Al-Sharqi', '+971-9-444-5555', '+971-55-444-5555', '<EMAIL>', 'Port Area, Warehouse Complex B', 'Fujairah', 'Fujairah', '33333', 'UAE', 'TRN444555666', 40000.0, 8000.0, 'Food and beverage distributor', 1, '2025-07-06 09:03:19.735455', '2025-07-06 09:03:19.735455')
2025-07-06 12:33:19,740 - sqlalchemy.engine.Engine - INFO - INSERT INTO customers (name, code, contact_person, phone, mobile, email, address, city, state, postal_code, country, tax_id, credit_limit, current_balance, notes, is_active, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-06 12:33:19,740 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 7/10 (ordered; batch not supported)] ('Ajman Steel Works', 'CUST007', 'Khalid Al-Nuaimi', '+971-6-666-7777', '+971-52-666-7777', '<EMAIL>', 'Heavy Industrial Area, Plot 89', 'Ajman', 'Ajman', '44444', 'UAE', 'TRN666777888', 120000.0, 35000.0, 'Steel fabrication and construction materials', 1, '2025-07-06 09:03:19.735455', '2025-07-06 09:03:19.735455')
2025-07-06 12:33:19,740 - sqlalchemy.engine.Engine - INFO - INSERT INTO customers (name, code, contact_person, phone, mobile, email, address, city, state, postal_code, country, tax_id, credit_limit, current_balance, notes, is_active, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-06 12:33:19,740 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 8/10 (ordered; batch not supported)] ('Umm Al Quwain Trading', 'CUST008', 'Nadia Al-Mualla', '+971-6-888-9999', '+971-56-888-9999', '<EMAIL>', 'Free Zone Area, Office 456', 'Umm Al Quwain', 'Umm Al Quwain', '55555', 'UAE', 'TRN888999000', 25000.0, 0.0, 'General trading company', 1, '2025-07-06 09:03:19.735455', '2025-07-06 09:03:19.735455')
2025-07-06 12:33:19,740 - sqlalchemy.engine.Engine - INFO - INSERT INTO customers (name, code, contact_person, phone, mobile, email, address, city, state, postal_code, country, tax_id, credit_limit, current_balance, notes, is_active, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-06 12:33:19,740 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 9/10 (ordered; batch not supported)] ('Dubai Tech Solutions', 'CUST009', 'Hassan Al-Maktoum', '+971-4-111-2222', '+971-50-111-2222', '<EMAIL>', 'Dubai Internet City, Building 7', 'Dubai', 'Dubai', '66666', 'UAE', 'TRN111222333', 60000.0, 12000.0, 'IT services and software solutions', 1, '2025-07-06 09:03:19.735455', '2025-07-06 09:03:19.735455')
2025-07-06 12:33:19,740 - sqlalchemy.engine.Engine - INFO - INSERT INTO customers (name, code, contact_person, phone, mobile, email, address, city, state, postal_code, country, tax_id, credit_limit, current_balance, notes, is_active, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-06 12:33:19,741 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 10/10 (ordered; batch not supported)] ('Inactive Customer Ltd.', 'CUST010', 'Test Contact', '+971-4-000-0000', '+971-50-000-0000', '<EMAIL>', 'Test Address', 'Dubai', 'Dubai', '00000', 'UAE', 'TRN000000000', 10000.0, 0.0, 'This is an inactive customer for testing', 0, '2025-07-06 09:03:19.735455', '2025-07-06 09:03:19.735455')
2025-07-06 12:33:19,741 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-06 13:14:13,233 - config.settings - INFO - Application starting in development mode
2025-07-06 13:14:13,236 - config.settings - INFO - Database: sqlite:///./data/rabie_alkhaleej.db
2025-07-06 13:14:13,283 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-06 13:14:13,284 - sqlalchemy.engine.Engine - INFO - INSERT INTO customers (name, contact_person, phone, mobile, email, address, city, state, postal_code, country, tax_id, credit_limit, current_balance, notes, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-07-06 13:14:13,284 - sqlalchemy.engine.Engine - INFO - [generated in 0.00037s] ('Test Customer Ltd.', 'John Doe', '+971-4-555-1234', '+971-50-555-1234', '<EMAIL>', '123 Test Street', 'Dubai', 'Dubai', '12345', 'UAE', 'TRN123456789', 25000.0, 0.0, 'Test customer added after removing code and status fields', '2025-07-06 09:44:13.284912', '2025-07-06 09:44:13.284912')
2025-07-06 13:14:13,286 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-06 13:14:13,293 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-06 13:14:13,295 - sqlalchemy.engine.Engine - INFO - SELECT customers.name AS customers_name, customers.contact_person AS customers_contact_person, customers.phone AS customers_phone, customers.mobile AS customers_mobile, customers.email AS customers_email, customers.address AS customers_address, customers.city AS customers_city, customers.state AS customers_state, customers.postal_code AS customers_postal_code, customers.country AS customers_country, customers.tax_id AS customers_tax_id, customers.credit_limit AS customers_credit_limit, customers.current_balance AS customers_current_balance, customers.notes AS customers_notes, customers.id AS customers_id, customers.created_at AS customers_created_at, customers.updated_at AS customers_updated_at 
FROM customers 
WHERE customers.id = ?
2025-07-06 13:14:13,296 - sqlalchemy.engine.Engine - INFO - [generated in 0.00032s] (11,)
2025-07-06 13:14:13,297 - sqlalchemy.engine.Engine - INFO - SELECT customers.name AS customers_name, customers.contact_person AS customers_contact_person, customers.phone AS customers_phone, customers.mobile AS customers_mobile, customers.email AS customers_email, customers.address AS customers_address, customers.city AS customers_city, customers.state AS customers_state, customers.postal_code AS customers_postal_code, customers.country AS customers_country, customers.tax_id AS customers_tax_id, customers.credit_limit AS customers_credit_limit, customers.current_balance AS customers_current_balance, customers.notes AS customers_notes, customers.id AS customers_id, customers.created_at AS customers_created_at, customers.updated_at AS customers_updated_at 
FROM customers 
WHERE customers.name = ?
 LIMIT ? OFFSET ?
2025-07-06 13:14:13,297 - sqlalchemy.engine.Engine - INFO - [generated in 0.00029s] ('Test Customer Ltd.', 1, 0)
2025-07-06 13:14:13,299 - sqlalchemy.engine.Engine - INFO - SELECT invoices.invoice_number AS invoices_invoice_number, invoices.invoice_date AS invoices_invoice_date, invoices.due_date AS invoices_due_date, invoices.status AS invoices_status, invoices.is_paid AS invoices_is_paid, invoices.subtotal AS invoices_subtotal, invoices.tax_amount AS invoices_tax_amount, invoices.discount_amount AS invoices_discount_amount, invoices.total_amount AS invoices_total_amount, invoices.payment_method AS invoices_payment_method, invoices.payment_date AS invoices_payment_date, invoices.payment_reference AS invoices_payment_reference, invoices.notes AS invoices_notes, invoices.terms_and_conditions AS invoices_terms_and_conditions, invoices.customer_id AS invoices_customer_id, invoices.user_id AS invoices_user_id, invoices.id AS invoices_id, invoices.created_at AS invoices_created_at, invoices.updated_at AS invoices_updated_at 
FROM invoices 
WHERE ? = invoices.customer_id
2025-07-06 13:14:13,299 - sqlalchemy.engine.Engine - INFO - [generated in 0.00030s] (11,)
2025-07-06 13:14:13,301 - sqlalchemy.engine.Engine - INFO - DELETE FROM customers WHERE customers.id = ?
2025-07-06 13:14:13,301 - sqlalchemy.engine.Engine - INFO - [generated in 0.00018s] (11,)
2025-07-06 13:14:13,302 - sqlalchemy.engine.Engine - INFO - COMMIT
