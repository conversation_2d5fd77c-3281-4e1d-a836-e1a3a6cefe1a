"""
Database utility functions and helpers.

This module provides utility functions for common database operations,
including session management, error handling, and query building.
"""
import logging
from typing import Any, Dict, List, Optional, Type, TypeVar, Union
from datetime import datetime
from contextlib import contextmanager

from sqlalchemy import create_engine, inspect, text
from sqlalchemy.exc import SQLAlchemyError, IntegrityError
from sqlalchemy.orm import Session, sessionmaker, scoped_session
from sqlalchemy.orm.exc import NoResultFound, MultipleResultsFound

from database.session import SessionLocal, engine

# Type variable for SQLAlchemy model classes
ModelType = TypeVar('ModelType')

logger = logging.getLogger(__name__)

@contextmanager
def get_db() -> Session:
    """
    Context manager for database sessions.
    
    Example:
        with get_db() as db:
            user = db.query(User).first()
    """
    db = SessionLocal()
    try:
        yield db
        db.commit()
    except Exception as e:
        db.rollback()
        logger.error(f"Database error: {e}")
        raise
    finally:
        db.close()

def get_object_or_none(
    db: Session, 
    model: Type[ModelType], 
    **filters: Any
) -> Optional[ModelType]:
    """
    Get a single object by filters or return None if not found.
    
    Args:
        db: Database session
        model: SQLAlchemy model class
        **filters: Filter conditions
        
    Returns:
        The first matching object or None if not found
    """
    try:
        return db.query(model).filter_by(**filters).first()
    except Exception as e:
        logger.error(f"Error getting object {model.__name__}: {e}")
        return None

def get_object_or_404(
    db: Session, 
    model: Type[ModelType], 
    object_id: Any,
    error_message: str = None
) -> ModelType:
    """
    Get a single object by ID or raise 404 if not found.
    
    Args:
        db: Database session
        model: SQLAlchemy model class
        object_id: ID of the object to retrieve
        error_message: Custom error message if object not found
        
    Returns:
        The requested object
        
    Raises:
        HTTPException: 404 if object not found
    """
    from fastapi import HTTPException, status
    
    obj = db.get(model, object_id)
    if obj is None:
        error_message = error_message or f"{model.__name__} with ID {object_id} not found"
        logger.warning(error_message)
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=error_message
        )
    return obj

def get_or_create(
    db: Session, 
    model: Type[ModelType],
    defaults: Optional[Dict[str, Any]] = None,
    **kwargs: Any
) -> tuple[ModelType, bool]:
    """
    Get an object or create it if it doesn't exist.
    
    Args:
        db: Database session
        model: SQLAlchemy model class
        defaults: Default values for new objects
        **kwargs: Filter conditions to find the object
        
    Returns:
        A tuple of (object, created) where created is a boolean
        indicating whether a new object was created
    """
    instance = db.query(model).filter_by(**kwargs).first()
    if instance:
        return instance, False
    
    try:
        params = {**kwargs, **(defaults or {})}
        instance = model(**params)
        db.add(instance)
        db.commit()
        return instance, True
    except IntegrityError as e:
        db.rollback()
        instance = db.query(model).filter_by(**kwargs).first()
        if instance is None:
            raise e
        return instance, False

def bulk_insert_or_update(
    db: Session,
    model: Type[ModelType],
    data: List[Dict[str, Any]],
    update_fields: Optional[List[str]] = None,
    commit: bool = True
) -> List[ModelType]:
    """
    Bulk insert or update multiple records.
    
    Args:
        db: Database session
        model: SQLAlchemy model class
        data: List of dictionaries with record data
        update_fields: List of fields to update if record exists
        commit: Whether to commit the transaction
        
    Returns:
        List of created/updated model instances
    """
    if not data:
        return []
    
    # Get primary key columns
    mapper = inspect(model)
    pk_columns = [column.key for column in mapper.primary_key]
    
    if not pk_columns:
        raise ValueError(f"Model {model.__name__} has no primary key")
    
    results = []
    
    try:
        for item in data:
            # Extract primary key values
            pk_values = {col: item.get(col) for col in pk_columns}
            
            # Check if record exists
            instance = db.query(model).filter_by(**pk_values).first()
            
            if instance:
                # Update existing record
                if update_fields:
                    for field in update_fields:
                        if field in item and field not in pk_columns:
                            setattr(instance, field, item[field])
                else:
                    for key, value in item.items():
                        if key not in pk_columns:
                            setattr(instance, key, value)
                results.append(instance)
            else:
                # Create new record
                instance = model(**item)
                db.add(instance)
                results.append(instance)
        
        if commit:
            db.commit()
            
        return results
    except Exception as e:
        db.rollback()
        logger.error(f"Error in bulk_insert_or_update: {e}")
        raise

def execute_raw_sql(
    db: Session, 
    sql: str, 
    params: Optional[Dict[str, Any]] = None
) -> List[Dict[str, Any]]:
    """
    Execute raw SQL and return results as dictionaries.
    
    Args:
        db: Database session
        sql: SQL query string
        params: Parameters for the SQL query
        
    Returns:
        List of dictionaries representing the query results
    """
    try:
        result = db.execute(text(sql), params or {})
        columns = result.keys()
        return [dict(zip(columns, row)) for row in result.fetchall()]
    except Exception as e:
        logger.error(f"Error executing SQL: {e}")
        raise

def get_table_columns(model: Type[ModelType]) -> List[str]:
    """
    Get the column names for a SQLAlchemy model.
    
    Args:
        model: SQLAlchemy model class
        
    Returns:
        List of column names
    """
    return [column.name for column in model.__table__.columns]

def model_to_dict(
    instance: ModelType, 
    exclude: Optional[List[str]] = None
) -> Dict[str, Any]:
    """
    Convert a SQLAlchemy model instance to a dictionary.
    
    Args:
        instance: SQLAlchemy model instance
        exclude: List of field names to exclude
        
    Returns:
        Dictionary representation of the model
    """
    if exclude is None:
        exclude = []
    
    result = {}
    for column in instance.__table__.columns:
        if column.name in exclude:
            continue
        
        value = getattr(instance, column.name)
        
        # Convert datetime objects to ISO format strings
        if hasattr(value, 'isoformat'):
            value = value.isoformat()
        
        result[column.name] = value
    
    return result
