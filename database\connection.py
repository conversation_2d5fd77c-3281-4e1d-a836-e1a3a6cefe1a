from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, scoped_session
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.exc import SQLAlchemyError
from config import settings
import logging

# Configure logging
logging.basicConfig()
logging.getLogger('sqlalchemy.engine').setLevel(logging.WARNING)

# Create SQLAlchemy engine
engine = None
SessionLocal = None
Base = declarative_base()

def init_db():
    """Initialize the database connection."""
    global engine, SessionLocal
    
    try:
        # Create engine
        engine = create_engine(
            f"mssql+pyodbc://{settings.DB_CONFIG['username']}:{settings.DB_CONFIG['password']}@"
            f"{settings.DB_CONFIG['host']}:{settings.DB_CONFIG['port']}/{settings.DB_CONFIG['database']}?"
            f"driver={settings.DB_CONFIG['query']['driver']}",
            echo=False,
            pool_pre_ping=True,
            pool_recycle=3600
        )
        
        # Create session factory
        SessionLocal = scoped_session(
            sessionmaker(autocommit=False, autoflush=False, bind=engine)
        )
        
        return True
    except Exception as e:
        print(f"Error initializing database: {e}")
        return False

def get_db():
    """
    Get a database session.
    
    Yields:
        Session: A database session.
    """
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

def close_db():
    """Close the database connection."""
    if engine:
        engine.dispose()
        print("Database connection closed.")

# Initialize database when this module is imported
init_db()
