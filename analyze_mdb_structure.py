import pyodbc
import pandas as pd
from pathlib import Path
from datetime import datetime

def get_access_tables(conn):
    """Get list of tables in the Access database."""
    cursor = conn.cursor()
    tables = []
    for table in cursor.tables(tableType='TABLE'):
        table_name = table.table_name
        if not table_name.startswith('MSys'):
            tables.append(table_name)
    return sorted(tables)

def get_table_info(conn, table_name):
    """Get information about a table's columns."""
    cursor = conn.cursor()
    columns = []
    
    # Get column information
    for col in cursor.columns(table=table_name):
        columns.append({
            'name': col.column_name,
            'type': col.type_name,
            'size': col.column_size,
            'nullable': col.nullable == 1,
            'primary_key': False  # Will be updated later
        })
    
    # Try to get primary key information
    try:
        pkeys = [row.column_name for row in cursor.primaryKeys(table=table_name)]
        for col in columns:
            if col['name'] in pkeys:
                col['primary_key'] = True
    except:
        pass  # Some drivers don't support primaryKeys()
    
    return columns

def get_sample_data(conn, table_name, limit=3):
    """Get sample data from a table."""
    try:
        query = f'SELECT TOP {limit} * FROM [{table_name}]'
        df = pd.read_sql(query, conn)
        return df.to_dict('records')
    except Exception as e:
        print(f"  Could not fetch sample data: {e}")
        return []

def analyze_database(db_path, username='', password=''):
    """Analyze an Access database and return its structure."""
    # Try different connection strings
    conn_strs = [
        f'DRIVER={{Microsoft Access Driver (*.mdb, *.accdb)}};DBQ={db_path}',
        f'DRIVER={{Microsoft Access Driver (*.mdb, *.accdb)}};DBQ={db_path};UID={username};PWD={password};',
        f'DRIVER={{Microsoft Access Driver (*.mdb)}};DBQ={db_path}',
        f'DRIVER={{Microsoft Access Driver (*.mdb)}};DBQ={db_path};UID={username};PWD={password};',
    ]
    
    conn = None
    for conn_str in conn_strs:
        try:
            conn = pyodbc.connect(conn_str)
            print(f"Successfully connected to database.")
            break
        except pyodbc.Error as e:
            continue
    
    if not conn:
        print("Failed to connect to the database.")
        return None
    
    try:
        # Get database structure
        tables = get_access_tables(conn)
        
        db_structure = {
            'database': Path(db_path).name,
            'tables': {},
            'analysis_date': datetime.now().isoformat()
        }
        
        # Analyze each table
        for table_name in tables:
            print(f"Analyzing table: {table_name}")
            
            columns = get_table_info(conn, table_name)
            sample_data = get_sample_data(conn, table_name)
            
            db_structure['tables'][table_name] = {
                'columns': columns,
                'sample_data': sample_data,
                'row_count': len(sample_data)  # Sample count, not total
            }
        
        return db_structure
        
    except Exception as e:
        print(f"Error analyzing database: {e}")
        return None
    finally:
        if conn:
            conn.close()

def save_analysis_to_file(structure, output_file):
    """Save the database structure to a text file."""
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(f"Database Analysis Report\n")
        f.write(f"Database: {structure['database']}\n")
        f.write(f"Analysis Date: {structure['analysis_date']}\n")
        f.write("=" * 80 + "\n\n")
        
        for table_name, table_info in structure['tables'].items():
            f.write(f"TABLE: {table_name}\n")
            f.write("-" * 80 + "\n")
            
            # Write column information
            f.write("COLUMNS:\n")
            for col in table_info['columns']:
                pk = " (PK)" if col['primary_key'] else ""
                nullable = "NULL" if col['nullable'] else "NOT NULL"
                f.write(f"  {col['name']}: {col['type']}({col['size'] or '?'}) {nullable}{pk}\n")
            
            # Write sample data
            if table_info['sample_data']:
                f.write("\nSAMPLE DATA:\n")
                for row in table_info['sample_data']:
                    f.write("  " + ", ".join(f"{k}: {v}" for k, v in row.items()) + "\n")
            
            f.write("\n" + "=" * 80 + "\n\n")
    
    print(f"Analysis saved to: {output_file}")

if __name__ == "__main__":
    # Database connection details
    db_path = r"c:\HamzaZareiProgram\RabieAl-KhaleejMulti.mdb"
    username = "majid"
    password = "majid"
    output_file = "database_analysis.txt"
    
    print(f"Analyzing database: {db_path}")
    
    # Check if database file exists
    if not Path(db_path).exists():
        print(f"Error: Database file not found at {db_path}")
    else:
        # Analyze the database
        structure = analyze_database(db_path, username, password)
        
        if structure:
            # Save the analysis to a file
            save_analysis_to_file(structure, output_file)
            
            # Print summary
            print("\n=== Analysis Complete ===")
            print(f"Tables found: {len(structure['tables'])}")
            print(f"Analysis saved to: {output_file}")
