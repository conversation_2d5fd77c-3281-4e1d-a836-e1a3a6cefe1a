#!/usr/bin/env python3
"""
Main entry point for the Rabie Al-Khaleej Accounting Application.
"""
import sys
import os
from pathlib import Path

def setup_environment():
    """Set up the Python environment and import paths."""
    # Add the project root to the Python path
    project_root = Path(__file__).parent.absolute()
    if str(project_root) not in sys.path:
        sys.path.insert(0, str(project_root))
    
    # Load environment variables
    from dotenv import load_dotenv
    load_dotenv()

def check_dependencies():
    """Check if all required dependencies are installed."""
    try:
        import PyQt6
        import sqlalchemy
        import pyodbc
        import pandas
        import reportlab
        import python_dotenv
        import sqlalchemy_utils
    except ImportError as e:
        print(f"Error: Missing required dependency - {e.name}")
        print("Please install all dependencies using:")
        print("pip install -r requirements.txt")
        sys.exit(1)

def init_database():
    """Initialize the database if needed."""
    from database.session import engine
    from sqlalchemy_utils import database_exists, create_database
    
    db_url = str(engine.url)
    
    # Create database if it doesn't exist
    if not database_exists(db_url):
        print("Creating database...")
        create_database(db_url)
    
    # Initialize database schema and data
    from scripts.init_db import main as init_db
    if not init_db():
        print("Failed to initialize database. See logs for details.")
        sys.exit(1)

def main():
    """Main entry point for the application."""
    # Set up environment
    setup_environment()
    
    # Check dependencies
    check_dependencies()
    
    # Initialize database
    init_database()
    
    # Import and start the application
    from PyQt6.QtWidgets import QApplication
    from ui.main_window import MainWindow
    from config import settings
    
    try:
        # Create the application
        app = QApplication(sys.argv)
        app.setApplicationName(settings.APP_NAME)
        app.setApplicationVersion(settings.VERSION)
        
        # Create and show the main window
        window = MainWindow()
        window.show()
        
        # Run the application
        sys.exit(app.exec())
        
    except Exception as e:
        print(f"Error: {e}", file=sys.stderr)
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())
